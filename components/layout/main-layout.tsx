'use client'

import { ReactNode } from 'react'
import { Head<PERSON> } from './header'
import { Footer } from './footer'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: ReactNode
  currentPage?: string
  showHeader?: boolean
  showFooter?: boolean
  headerClassName?: string
  footerClassName?: string
  className?: string
}

export function MainLayout({
  children,
  currentPage = 'home',
  showHeader = true,
  showFooter = true,
  headerClassName,
  footerClassName,
  className,
}: MainLayoutProps) {
  return (
    <div className={cn('min-h-screen bg-white text-gray-900', className)}>
      {showHeader && (
        <Header currentPage={currentPage} className={headerClassName} />
      )}
      
      <main className={showHeader ? 'pt-20' : ''}>
        {children}
      </main>
      
      {showFooter && (
        <Footer className={footerClassName} />
      )}
    </div>
  )
}
