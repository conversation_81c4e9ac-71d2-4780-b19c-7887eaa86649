"use client"

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

interface AnalyticsProps {
  trackingId?: string
  enabled?: boolean
}

export function Analytics({ trackingId, enabled = process.env.NODE_ENV === 'production' }: AnalyticsProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!enabled || !trackingId || typeof window === 'undefined') return

    // Initialize Google Analytics
    const script = document.createElement('script')
    script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`
    script.async = true
    document.head.appendChild(script)

    const configScript = document.createElement('script')
    configScript.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${trackingId}', {
        page_title: document.title,
        page_location: window.location.href,
      });
    `
    document.head.appendChild(configScript)

    return () => {
      document.head.removeChild(script)
      document.head.removeChild(configScript)
    }
  }, [trackingId, enabled])

  useEffect(() => {
    if (!enabled || !trackingId || typeof window === 'undefined') return

    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '')
    
    // Track page views
    if (window.gtag) {
      window.gtag('config', trackingId, {
        page_path: url,
        page_title: document.title,
      })
    }
  }, [pathname, searchParams, trackingId, enabled])

  return null
}

// Utility function to track custom events
export function trackEvent(eventName: string, parameters?: Record<string, any>) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters)
  }
}

// Utility function to track conversions
export function trackConversion(conversionId: string, value?: number, currency = 'USD') {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'conversion', {
      send_to: conversionId,
      value: value,
      currency: currency,
    })
  }
}

// Extend the Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}
