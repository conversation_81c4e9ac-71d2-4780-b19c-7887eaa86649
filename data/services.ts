import { Service, ServiceCategory } from '@/types'

export const services: Service[] = [
  {
    id: 'web-development',
    title: 'Web Development',
    description: 'Creating stunning, responsive websites that captivate audiences and drive conversions through thoughtful design and seamless user experience.',
    features: [
      'Responsive Design',
      'Performance Optimization',
      'SEO Ready',
      'Modern Frameworks',
      'Progressive Web Apps',
      'E-commerce Solutions',
    ],
    icon: 'Code',
    category: ServiceCategory.WEB_DEVELOPMENT,
  },
  {
    id: 'brand-identity',
    title: 'Brand Identity',
    description: 'Developing powerful brand identities that resonate with your target audience and establish lasting connections in the marketplace.',
    features: [
      'Logo Design',
      'Brand Guidelines',
      'Visual Identity',
      'Brand Strategy',
      'Color Palette',
      'Typography Systems',
    ],
    icon: 'Palette',
    category: ServiceCategory.BRAND_IDENTITY,
  },
  {
    id: 'ui-ux-design',
    title: 'UI/UX Design',
    description: 'Designing intuitive interfaces and user experiences that prioritize usability while maintaining aesthetic excellence and brand consistency.',
    features: [
      'User Research',
      'Wireframing',
      'Prototyping',
      'Usability Testing',
      'Information Architecture',
      'Interaction Design',
    ],
    icon: 'Smartphone',
    category: ServiceCategory.UI_UX_DESIGN,
  },
  {
    id: 'consultation',
    title: 'Digital Strategy',
    description: 'Providing expert guidance and strategic insights to help businesses navigate the digital landscape and achieve their goals.',
    features: [
      'Digital Audits',
      'Strategy Planning',
      'Technology Consulting',
      'Performance Analysis',
      'Growth Optimization',
      'Team Training',
    ],
    icon: 'Target',
    category: ServiceCategory.CONSULTATION,
  },
]

export function getServiceById(id: string): Service | undefined {
  return services.find(service => service.id === id)
}

export function getServicesByCategory(category: ServiceCategory): Service[] {
  return services.filter(service => service.category === category)
}

export function getFeaturedServices(): Service[] {
  return services.slice(0, 3) // Return first 3 services as featured
}
