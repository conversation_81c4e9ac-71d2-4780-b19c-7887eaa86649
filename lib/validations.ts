import { z } from 'zod'
import { ServiceCategory, BudgetRange } from '@/types'
import { VALIDATION_CONFIG, ERROR_MESSAGES } from './constants'

// Contact Form Schema
export const contactFormSchema = z.object({
  firstName: z
    .string()
    .min(VALIDATION_CONFIG.name.minLength, ERROR_MESSAGES.minLength(VALIDATION_CONFIG.name.minLength))
    .max(VALIDATION_CONFIG.name.maxLength, ERROR_MESSAGES.maxLength(VALIDATION_CONFIG.name.maxLength))
    .trim(),
  
  lastName: z
    .string()
    .min(VALIDATION_CONFIG.name.minLength, ERROR_MESSAGES.minLength(VALIDATION_CONFIG.name.minLength))
    .max(VALIDATION_CONFIG.name.maxLength, ERROR_MESSAGES.maxLength(VALIDATION_CONFIG.name.maxLength))
    .trim(),
  
  email: z
    .string()
    .email(ERROR_MESSAGES.email)
    .trim()
    .toLowerCase(),
  
  company: z
    .string()
    .max(VALIDATION_CONFIG.company.maxLength, ERROR_MESSAGES.maxLength(VALIDATION_CONFIG.company.maxLength))
    .trim()
    .optional(),
  
  service: z
    .nativeEnum(ServiceCategory)
    .optional(),
  
  budget: z
    .nativeEnum(BudgetRange)
    .optional(),
  
  message: z
    .string()
    .min(VALIDATION_CONFIG.message.minLength, ERROR_MESSAGES.minLength(VALIDATION_CONFIG.message.minLength))
    .max(VALIDATION_CONFIG.message.maxLength, ERROR_MESSAGES.maxLength(VALIDATION_CONFIG.message.maxLength))
    .trim(),
  
  privacyConsent: z
    .boolean()
    .refine(val => val === true, {
      message: 'You must agree to the privacy policy',
    }),
})

export type ContactFormData = z.infer<typeof contactFormSchema>

// Newsletter Subscription Schema
export const newsletterSchema = z.object({
  email: z
    .string()
    .email(ERROR_MESSAGES.email)
    .trim()
    .toLowerCase(),
})

export type NewsletterData = z.infer<typeof newsletterSchema>

// Blog Search Schema
export const blogSearchSchema = z.object({
  query: z.string().trim().optional(),
  category: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
})

export type BlogSearchParams = z.infer<typeof blogSearchSchema>

// Portfolio Filter Schema
export const portfolioFilterSchema = z.object({
  category: z.string().optional(),
  featured: z.boolean().optional(),
  limit: z.number().min(1).max(50).default(12),
})

export type PortfolioFilterParams = z.infer<typeof portfolioFilterSchema>

// Author Schema
export const authorSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  email: z.string().email(),
  avatar: z.string().url().optional(),
  bio: z.string().optional(),
  socialLinks: z.object({
    twitter: z.string().url().optional(),
    linkedin: z.string().url().optional(),
    github: z.string().url().optional(),
    dribbble: z.string().url().optional(),
    instagram: z.string().url().optional(),
  }).optional(),
})

export type AuthorData = z.infer<typeof authorSchema>

// Blog Post Schema
export const blogPostSchema = z.object({
  id: z.string(),
  title: z.string().min(1).max(200),
  excerpt: z.string().min(1).max(500),
  content: z.string().min(1),
  author: authorSchema,
  publishedAt: z.date(),
  readTime: z.string(),
  category: z.string(),
  tags: z.array(z.string()),
  image: z.string().url(),
  featured: z.boolean().default(false),
  slug: z.string().min(1),
})

export type BlogPostData = z.infer<typeof blogPostSchema>

// Service Schema
export const serviceSchema = z.object({
  id: z.string(),
  title: z.string().min(1),
  description: z.string().min(1),
  features: z.array(z.string()),
  icon: z.string(),
  category: z.nativeEnum(ServiceCategory),
})

export type ServiceData = z.infer<typeof serviceSchema>

// Portfolio Item Schema
export const portfolioItemSchema = z.object({
  id: z.string(),
  title: z.string().min(1),
  client: z.string().min(1),
  category: z.string(),
  description: z.string().min(1),
  image: z.string().url(),
  tags: z.array(z.string()),
  featured: z.boolean().default(false),
  url: z.string().url().optional(),
  completedAt: z.date(),
})

export type PortfolioItemData = z.infer<typeof portfolioItemSchema>

// Testimonial Schema
export const testimonialSchema = z.object({
  id: z.string(),
  quote: z.string().min(1),
  author: authorSchema,
  company: z.string().min(1),
  rating: z.number().min(1).max(5),
  image: z.string().url().optional(),
  featured: z.boolean().default(false),
})

export type TestimonialData = z.infer<typeof testimonialSchema>

// API Response Schema
export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
    message: z.string().optional(),
    success: z.boolean(),
  })

// Paginated Response Schema
export const paginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: z.array(dataSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    }),
  })

// Performance Metrics Schema
export const performanceMetricsSchema = z.object({
  lcp: z.number().optional(),
  fid: z.number().optional(),
  cls: z.number().optional(),
  fcp: z.number().optional(),
  ttfb: z.number().optional(),
})

export type PerformanceMetricsData = z.infer<typeof performanceMetricsSchema>

// Error Schema
export const errorSchema = z.object({
  message: z.string(),
  code: z.string().optional(),
  statusCode: z.number().optional(),
  stack: z.string().optional(),
})

export type ErrorData = z.infer<typeof errorSchema>

// Validation Helper Functions
export function validateContactForm(data: unknown): ContactFormData {
  return contactFormSchema.parse(data)
}

export function validateNewsletter(data: unknown): NewsletterData {
  return newsletterSchema.parse(data)
}

export function validateBlogSearch(data: unknown): BlogSearchParams {
  return blogSearchSchema.parse(data)
}

export function validatePortfolioFilter(data: unknown): PortfolioFilterParams {
  return portfolioFilterSchema.parse(data)
}

// Safe validation functions that return results instead of throwing
export function safeValidateContactForm(data: unknown) {
  return contactFormSchema.safeParse(data)
}

export function safeValidateNewsletter(data: unknown) {
  return newsletterSchema.safeParse(data)
}

export function safeValidateBlogSearch(data: unknown) {
  return blogSearchSchema.safeParse(data)
}

export function safeValidatePortfolioFilter(data: unknown) {
  return portfolioFilterSchema.safeParse(data)
}
