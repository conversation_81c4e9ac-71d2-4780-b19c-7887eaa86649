"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

interface Portfolio3DCardProps {
  title: string
  client: string
  category: string
  image: string
  className?: string
}

export function Portfolio3DCard({ title, client, category, image, className }: Portfolio3DCardProps) {
  const [rotation, setRotation] = useState({ x: 0, y: 0 })
  const [isHovered, setIsHovered] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [reducedMotion, setReducedMotion] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number>()

  // Check device capabilities and user preferences
  useEffect(() => {
    const checkCapabilities = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)

      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches
      setReducedMotion(prefersReducedMotion)
    }

    checkCapabilities()
    window.addEventListener("resize", checkCapabilities)
    return () => window.removeEventListener("resize", checkCapabilities)
  }, [])

  // Throttled mouse move handler using requestAnimationFrame
  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (isMobile || reducedMotion) return

      // Cancel previous animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        const card = cardRef.current
        if (!card) return

        const rect = card.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        // Reduced rotation range for better performance (was 8, now 4)
        const rotateY = ((e.clientX - centerX) / (rect.width / 2)) * 4
        const rotateX = ((centerY - e.clientY) / (rect.height / 2)) * 4

        setRotation({ x: rotateX, y: rotateY })
      })
    },
    [isMobile, reducedMotion],
  )

  // Reset rotation when mouse leaves
  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
    setRotation({ x: 0, y: 0 })

    // Cancel any pending animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
  }, [])

  // Set hover state
  const handleMouseEnter = useCallback(() => {
    if (!reducedMotion) {
      setIsHovered(true)
    }
  }, [reducedMotion])

  // For mobile: simple tap/touch interaction
  const handleTap = useCallback(() => {
    if (isMobile) {
      setIsHovered(!isHovered)
    }
  }, [isMobile, isHovered])

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Simplified transform for better performance
  const getCardTransform = () => {
    if (reducedMotion) {
      return isHovered ? "scale(1.02)" : "scale(1)"
    }

    if (isMobile) {
      return isHovered ? "translateZ(10px) scale(1.02)" : "translateZ(0px)"
    }

    return `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) ${isHovered ? "translateZ(10px) scale(1.02)" : ""}`
  }

  return (
    <div
      ref={cardRef}
      className={cn("relative cursor-pointer w-full h-full", className)}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTap}
      style={{
        perspective: reducedMotion ? "none" : "1000px",
      }}
    >
      <div
        className={cn(
          "relative w-full h-full transition-all duration-200 will-change-transform",
          isHovered ? "shadow-2xl" : "shadow-lg",
        )}
        style={{
          transformStyle: reducedMotion ? "flat" : "preserve-3d",
          transform: getCardTransform(),
        }}
      >
        {/* Card Content */}
        <div className="relative overflow-hidden rounded-lg bg-white border border-gray-200">
          {/* Image Container */}
          <div className="aspect-[4/3] overflow-hidden">
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              width={400}
              height={300}
              className={cn(
                "w-full h-full object-cover transition-transform duration-300 will-change-transform",
                isHovered && !reducedMotion ? "scale-105" : "",
              )}
              loading="lazy"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />

            {/* Category Badge */}
            <div className="absolute top-4 left-4">
              <Badge
                variant="secondary"
                className="bg-emerald-500/90 text-white border-0 shadow-lg backdrop-blur-sm px-3 py-1 text-xs font-medium"
              >
                {category}
              </Badge>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-5 relative">
            <h3 className="text-xl font-bold mb-1 text-gray-900 transition-colors duration-200">{title}</h3>
            <p className="text-gray-600 font-medium">{client}</p>

            {/* Accent Line - Simplified animation */}
            <div
              className={cn(
                "absolute bottom-0 left-5 right-5 h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 transition-all duration-300",
                isHovered ? "opacity-100 scale-x-100" : "opacity-0 scale-x-0",
              )}
              style={{
                transformOrigin: "left center",
              }}
            />
          </div>

          {/* Action Button - Simplified */}
          <div
            className={cn(
              "absolute top-4 right-4 transition-all duration-200",
              isHovered ? "opacity-100" : "opacity-0",
            )}
          >
            <Button
              size="sm"
              className="bg-white/90 text-emerald-600 hover:bg-white hover:text-emerald-700 shadow-lg backdrop-blur-sm border border-emerald-200/50 rounded-full w-10 h-10 p-0"
            >
              <ExternalLink className="w-4 h-4" />
              <span className="sr-only">View project</span>
            </Button>
          </div>
        </div>

        {/* Simplified Bottom Shadow */}
        {!reducedMotion && (
          <div
            className={cn(
              "absolute -bottom-1 left-4 right-4 h-2 bg-black/5 blur-sm rounded-full transition-opacity duration-200",
              isHovered ? "opacity-100" : "opacity-50",
            )}
          />
        )}
      </div>

      {/* Mobile Overlay - Simplified */}
      {isMobile && (
        <div
          className={cn(
            "absolute inset-0 bg-emerald-600/80 flex items-center justify-center transition-opacity duration-200 rounded-lg",
            isHovered ? "opacity-100" : "opacity-0 pointer-events-none",
          )}
        >
          <div className="text-center text-white p-6">
            <Badge variant="secondary" className="mb-3 bg-white/20 text-white border-white/30">
              {category}
            </Badge>
            <h3 className="text-xl font-bold mb-2">{title}</h3>
            <p className="text-base opacity-90">{client}</p>
            <Button className="mt-4 bg-white text-emerald-600 hover:bg-white/90" size="sm">
              View Project
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
