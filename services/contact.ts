import { ContactForm, ApiResponse } from '@/types'
import { validateContactForm } from '@/lib/validations'
import { sleep } from '@/lib/utils'

// Simulate API delay
const SIMULATED_DELAY = 1000

/**
 * Contact Service
 * Handles contact form submissions and newsletter subscriptions
 * In a real application, this would interface with email services, CRM, etc.
 */
export class ContactService {
  /**
   * Submit contact form
   */
  static async submitContactForm(data: unknown): Promise<ApiResponse<{ id: string }>> {
    try {
      // Validate the form data
      const validatedData = validateContactForm(data)
      
      // Simulate API call delay
      await sleep(SIMULATED_DELAY)
      
      // Simulate random failure for testing (10% chance)
      if (Math.random() < 0.1) {
        throw new Error('Failed to send message. Please try again.')
      }
      
      // In a real application, you would:
      // 1. Send email notification
      // 2. Store in database/CRM
      // 3. Send auto-reply to user
      // 4. Trigger any automation workflows
      
      console.log('Contact form submitted:', validatedData)
      
      // Generate a mock submission ID
      const submissionId = `contact_${Date.now()}_${Math.random().toString(36).substring(2)}`
      
      return {
        success: true,
        data: { id: submissionId },
        message: 'Thank you for your message! We\'ll get back to you within 24 hours.',
      }
    } catch (error) {
      console.error('Contact form submission error:', error)
      
      return {
        success: false,
        data: { id: '' },
        message: error instanceof Error ? error.message : 'Failed to send message. Please try again.',
      }
    }
  }

  /**
   * Subscribe to newsletter
   */
  static async subscribeToNewsletter(email: string): Promise<ApiResponse<{ subscribed: boolean }>> {
    try {
      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address.')
      }
      
      // Simulate API call delay
      await sleep(SIMULATED_DELAY)
      
      // Simulate random failure for testing (5% chance)
      if (Math.random() < 0.05) {
        throw new Error('Failed to subscribe. Please try again.')
      }
      
      // In a real application, you would:
      // 1. Check if email already exists
      // 2. Add to email marketing service (Mailchimp, ConvertKit, etc.)
      // 3. Send welcome email
      // 4. Update user preferences
      
      console.log('Newsletter subscription:', email)
      
      return {
        success: true,
        data: { subscribed: true },
        message: 'Successfully subscribed! Check your email for confirmation.',
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error)
      
      return {
        success: false,
        data: { subscribed: false },
        message: error instanceof Error ? error.message : 'Failed to subscribe. Please try again.',
      }
    }
  }

  /**
   * Get contact information
   */
  static async getContactInfo(): Promise<{
    email: string
    phone: string
    address: {
      street: string
      city: string
      state: string
      zip: string
    }
    hours: {
      weekdays: string
      saturday: string
      sunday: string
    }
    social: {
      twitter: string
      linkedin: string
      instagram: string
      dribbble: string
    }
  }> {
    await sleep(100) // Minimal delay for consistency
    
    return {
      email: '<EMAIL>',
      phone: '+****************',
      address: {
        street: '123 Design Street',
        city: 'Creative District',
        state: 'CD',
        zip: '12345',
      },
      hours: {
        weekdays: '9:00 AM - 6:00 PM',
        saturday: '10:00 AM - 4:00 PM',
        sunday: 'Closed',
      },
      social: {
        twitter: 'https://twitter.com/dsfsolutions',
        linkedin: 'https://linkedin.com/company/dsf-solutions',
        instagram: 'https://instagram.com/dsfsolutions',
        dribbble: 'https://dribbble.com/dsfsolutions',
      },
    }
  }

  /**
   * Send project inquiry
   */
  static async sendProjectInquiry(data: {
    name: string
    email: string
    company?: string
    projectType: string
    budget: string
    timeline: string
    description: string
  }): Promise<ApiResponse<{ id: string }>> {
    try {
      // Simulate API call delay
      await sleep(SIMULATED_DELAY)
      
      // Basic validation
      if (!data.name || !data.email || !data.description) {
        throw new Error('Please fill in all required fields.')
      }
      
      // Simulate random failure for testing (5% chance)
      if (Math.random() < 0.05) {
        throw new Error('Failed to send inquiry. Please try again.')
      }
      
      console.log('Project inquiry submitted:', data)
      
      const inquiryId = `inquiry_${Date.now()}_${Math.random().toString(36).substring(2)}`
      
      return {
        success: true,
        data: { id: inquiryId },
        message: 'Thank you for your project inquiry! We\'ll review your requirements and get back to you soon.',
      }
    } catch (error) {
      console.error('Project inquiry error:', error)
      
      return {
        success: false,
        data: { id: '' },
        message: error instanceof Error ? error.message : 'Failed to send inquiry. Please try again.',
      }
    }
  }

  /**
   * Request quote
   */
  static async requestQuote(data: {
    contactInfo: {
      name: string
      email: string
      company?: string
      phone?: string
    }
    projectDetails: {
      type: string
      scope: string[]
      timeline: string
      budget: string
      description: string
    }
    requirements: {
      features: string[]
      integrations: string[]
      platforms: string[]
    }
  }): Promise<ApiResponse<{ quoteId: string; estimatedDelivery: string }>> {
    try {
      // Simulate API call delay
      await sleep(SIMULATED_DELAY * 1.5) // Longer delay for quote requests
      
      // Simulate random failure for testing (3% chance)
      if (Math.random() < 0.03) {
        throw new Error('Failed to process quote request. Please try again.')
      }
      
      console.log('Quote request submitted:', data)
      
      const quoteId = `quote_${Date.now()}_${Math.random().toString(36).substring(2)}`
      const estimatedDelivery = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days from now
      
      return {
        success: true,
        data: { 
          quoteId,
          estimatedDelivery,
        },
        message: 'Quote request received! We\'ll prepare a detailed proposal and send it to you within 3 business days.',
      }
    } catch (error) {
      console.error('Quote request error:', error)
      
      return {
        success: false,
        data: { quoteId: '', estimatedDelivery: '' },
        message: error instanceof Error ? error.message : 'Failed to process quote request. Please try again.',
      }
    }
  }
}
