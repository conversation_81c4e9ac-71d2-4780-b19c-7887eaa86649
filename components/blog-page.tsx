"use client"

import { useState, useMemo, useCallback } from "react"
import Image from "next/image"
import { Search, Calendar, Clock, User, ArrowRight, Tag, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  date: string
  readTime: string
  category: string
  tags: string[]
  image: string
  featured: boolean
}

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [currentPage, setCurrentPage] = useState(1)
  const postsPerPage = 6

  const blogPosts: BlogPost[] = useMemo(() => [
    {
      id: "1",
      title: "The Future of Web Design: Trends to Watch in 2024",
      excerpt:
        "Explore the cutting-edge design trends that are shaping the digital landscape and how they can transform your brand's online presence.",
      content: "Full blog content here...",
      author: "<PERSON>",
      date: "2024-01-15",
      readTime: "8 min read",
      category: "Design",
      tags: ["Web Design", "Trends", "UI/UX"],
      image: "https://picsum.photos/800/400?random=1",
      featured: true,
    },
    {
      id: "2",
      title: "Building High-Performance React Applications",
      excerpt:
        "Learn the best practices and optimization techniques to create lightning-fast React applications that deliver exceptional user experiences.",
      content: "Full blog content here...",
      author: "Michael Chen",
      date: "2024-01-12",
      readTime: "12 min read",
      category: "Development",
      tags: ["React", "Performance", "JavaScript"],
      image: "https://picsum.photos/800/400?random=2",
      featured: true,
    },
    {
      id: "3",
      title: "Brand Identity Design: Creating Memorable Visual Stories",
      excerpt:
        "Discover how to craft compelling brand identities that resonate with your audience and stand out in today's competitive market.",
      content: "Full blog content here...",
      author: "Emily Rodriguez",
      date: "2024-01-10",
      readTime: "6 min read",
      category: "Branding",
      tags: ["Branding", "Identity", "Design"],
      image: "https://picsum.photos/800/400?random=3",
      featured: false,
    },
    {
      id: "4",
      title: "The Complete Guide to SEO for Modern Websites",
      excerpt:
        "Master the art of search engine optimization with our comprehensive guide covering the latest SEO strategies and techniques.",
      content: "Full blog content here...",
      author: "David Kim",
      date: "2024-01-08",
      readTime: "15 min read",
      category: "Marketing",
      tags: ["SEO", "Marketing", "Web Development"],
      image: "https://picsum.photos/800/400?random=4",
      featured: false,
    },
    {
      id: "5",
      title: "Mobile-First Design: Why It Matters More Than Ever",
      excerpt:
        "Understanding the importance of mobile-first design approach and how it impacts user experience and business success.",
      content: "Full blog content here...",
      author: "Lisa Wang",
      date: "2024-01-05",
      readTime: "7 min read",
      category: "Design",
      tags: ["Mobile Design", "UX", "Responsive"],
      image: "https://picsum.photos/800/400?random=5",
      featured: false,
    },
    {
      id: "6",
      title: "E-commerce Conversion Optimization Strategies",
      excerpt:
        "Proven techniques to boost your online store's conversion rates and maximize revenue through strategic design and UX improvements.",
      content: "Full blog content here...",
      author: "James Wilson",
      date: "2024-01-03",
      readTime: "10 min read",
      category: "E-commerce",
      tags: ["E-commerce", "Conversion", "UX"],
      image: "https://picsum.photos/800/400?random=6",
      featured: false,
    },
  ], [])

  const categories = useMemo(() => ["All", "Design", "Development", "Branding", "Marketing", "E-commerce"], [])

  const featuredPosts = useMemo(() => blogPosts.filter((post) => post.featured), [blogPosts])

  const filteredPosts = useMemo(() => {
    return blogPosts.filter((post) => {
      const matchesSearch =
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      const matchesCategory = selectedCategory === "All" || post.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [blogPosts, searchTerm, selectedCategory])

  const totalPages = useMemo(() => Math.ceil(filteredPosts.length / postsPerPage), [filteredPosts.length, postsPerPage])

  const currentPosts = useMemo(() => {
    const startIndex = (currentPage - 1) * postsPerPage
    return filteredPosts.slice(startIndex, startIndex + postsPerPage)
  }, [filteredPosts, currentPage, postsPerPage])

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }, [])

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1) // Reset to first page when searching
  }, [])

  const handleCategoryChange = useCallback((category: string) => {
    setSelectedCategory(category)
    setCurrentPage(1) // Reset to first page when changing category
  }, [])

  const clearFilters = useCallback(() => {
    setSearchTerm("")
    setSelectedCategory("All")
    setCurrentPage(1)
  }, [])

  return (
    <div className="min-h-screen bg-white">
      {/* Blog Hero Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-br from-emerald-50/50 to-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-16">
            <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
              Insights & Inspiration
            </Badge>
            <h1 className="text-4xl sm:text-6xl md:text-7xl font-bold tracking-tight mb-4 sm:mb-6">
              OUR <span className="text-emerald-500">BLOG</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Stay ahead of the curve with expert insights, industry trends, and practical tips from our team of digital
              professionals.
            </p>
          </div>

          {/* Search and Filter */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-10 py-3 text-base"
                />
              </div>
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-full sm:w-48 py-3">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      {featuredPosts.length > 0 && (
        <section className="py-16 sm:py-20 bg-white">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight mb-4">
                Featured <span className="text-emerald-500">Articles</span>
              </h2>
              <p className="text-gray-600">Our most popular and impactful content</p>
            </div>

            <div className="grid gap-8 lg:grid-cols-2">
              {featuredPosts.map((post, index) => (
                <Card key={post.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="aspect-[16/9] overflow-hidden">
                    <Image
                      src={post.image || "/placeholder.svg"}
                      alt={post.title}
                      width={800}
                      height={400}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                  <CardHeader className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">
                        {post.category}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {formatDate(post.date)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {post.readTime}
                      </div>
                    </div>
                    <CardTitle className="text-xl sm:text-2xl font-bold leading-tight mb-3 group-hover:text-emerald-600 transition-colors">
                      {post.title}
                    </CardTitle>
                    <CardDescription className="text-gray-600 leading-relaxed mb-4">{post.excerpt}</CardDescription>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{post.author}</span>
                      </div>
                      <Button variant="ghost" className="text-emerald-600 hover:text-emerald-700 p-0">
                        Read More <ArrowRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Articles */}
      <section className="py-16 sm:py-20 bg-emerald-50/30">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight mb-4">
              All <span className="text-emerald-500">Articles</span>
            </h2>
            <p className="text-gray-600">
              {filteredPosts.length} article{filteredPosts.length !== 1 ? "s" : ""} found
              {selectedCategory !== "All" && ` in ${selectedCategory}`}
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>

          {currentPosts.length > 0 ? (
            <>
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {currentPosts.map((post) => (
                  <Card
                    key={post.id}
                    className="group hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col"
                  >
                    <div className="aspect-[16/10] overflow-hidden">
                      <Image
                        src={post.image || "/placeholder.svg"}
                        alt={post.title}
                        width={400}
                        height={250}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <CardHeader className="p-6 flex-1 flex flex-col">
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                        <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">
                          {post.category}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {post.readTime}
                        </div>
                      </div>
                      <CardTitle className="text-lg font-bold leading-tight mb-3 group-hover:text-emerald-600 transition-colors">
                        {post.title}
                      </CardTitle>
                      <CardDescription className="text-gray-600 leading-relaxed mb-4 flex-1">
                        {post.excerpt}
                      </CardDescription>
                      <div className="flex items-center justify-between mt-auto">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{post.author}</span>
                        </div>
                        <span className="text-sm text-gray-500">{formatDate(post.date)}</span>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4">
                        {post.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            <Tag className="w-3 h-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-2 mt-12">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => setCurrentPage(page)}
                      className={currentPage === page ? "bg-emerald-500 hover:bg-emerald-600" : ""}
                    >
                      {page}
                    </Button>
                  ))}

                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto mb-4" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No articles found</h3>
              <p className="text-gray-500 mb-6">Try adjusting your search terms or category filter</p>
              <Button
                variant="outline"
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-16 sm:py-20 bg-emerald-600 text-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-4">
              Stay Updated with Our <span className="text-emerald-200">Latest Insights</span>
            </h2>
            <p className="text-lg sm:text-xl text-emerald-100 mb-8 leading-relaxed">
              Get the latest articles, industry trends, and expert tips delivered straight to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="bg-white/10 border-white/20 text-white placeholder:text-emerald-200 focus:bg-white/20"
              />
              <Button className="bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8">Subscribe</Button>
            </div>
            <p className="text-sm text-emerald-200 mt-4">No spam, unsubscribe at any time. We respect your privacy.</p>
          </div>
        </div>
      </section>
    </div>
  )
}
