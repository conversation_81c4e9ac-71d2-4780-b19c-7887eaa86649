'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useContactForm } from '@/hooks/use-contact-form'
import { SERVICE_CATEGORIES, BUDGET_RANGES } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface ContactFormProps {
  className?: string
  onSuccess?: () => void
}

export function ContactForm({ className, onSuccess }: ContactFormProps) {
  const {
    form,
    onSubmit,
    isSubmitting,
    submitError,
    submitSuccess,
    clearMessages,
  } = useContactForm({
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const {
    register,
    formState: { errors },
    setValue,
    watch,
  } = form

  const watchedPrivacyConsent = watch('privacyConsent')

  return (
    <form onSubmit={onSubmit} className={cn('space-y-6', className)}>
      {/* Success Message */}
      {submitSuccess && (
        <div className="p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
          <p className="text-emerald-700 font-medium">
            Thank you for your message! We'll get back to you within 24 hours.
          </p>
        </div>
      )}

      {/* Error Message */}
      {submitError && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 font-medium">{submitError}</p>
          <Button
            type="button"
            variant="link"
            className="text-red-600 hover:text-red-700 p-0 h-auto mt-2"
            onClick={clearMessages}
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Name Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div className="space-y-2">
          <Label htmlFor="firstName">
            First Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="firstName"
            placeholder="John"
            {...register('firstName')}
            className={errors.firstName ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.firstName && (
            <p className="text-sm text-red-600" role="alert">
              {errors.firstName.message}
            </p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">
            Last Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="lastName"
            placeholder="Doe"
            {...register('lastName')}
            className={errors.lastName ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.lastName && (
            <p className="text-sm text-red-600" role="alert">
              {errors.lastName.message}
            </p>
          )}
        </div>
      </div>

      {/* Email */}
      <div className="space-y-2">
        <Label htmlFor="email">
          Email Address <span className="text-red-500">*</span>
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...register('email')}
          className={errors.email ? 'border-red-500' : ''}
          disabled={isSubmitting}
        />
        {errors.email && (
          <p className="text-sm text-red-600" role="alert">
            {errors.email.message}
          </p>
        )}
      </div>

      {/* Company */}
      <div className="space-y-2">
        <Label htmlFor="company">Company</Label>
        <Input
          id="company"
          placeholder="Your Company"
          {...register('company')}
          disabled={isSubmitting}
        />
      </div>

      {/* Service */}
      <div className="space-y-2">
        <Label htmlFor="service">Service Interested In</Label>
        <Select
          onValueChange={(value) => setValue('service', value as any)}
          disabled={isSubmitting}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a service" />
          </SelectTrigger>
          <SelectContent>
            {SERVICE_CATEGORIES.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Budget */}
      <div className="space-y-2">
        <Label htmlFor="budget">Project Budget</Label>
        <Select
          onValueChange={(value) => setValue('budget', value as any)}
          disabled={isSubmitting}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select budget range" />
          </SelectTrigger>
          <SelectContent>
            {BUDGET_RANGES.map((range) => (
              <SelectItem key={range.value} value={range.value}>
                {range.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Message */}
      <div className="space-y-2">
        <Label htmlFor="message">
          Project Details <span className="text-red-500">*</span>
        </Label>
        <Textarea
          id="message"
          placeholder="Tell us about your project, goals, and timeline..."
          className={cn('min-h-[120px]', errors.message ? 'border-red-500' : '')}
          {...register('message')}
          disabled={isSubmitting}
        />
        {errors.message && (
          <p className="text-sm text-red-600" role="alert">
            {errors.message.message}
          </p>
        )}
      </div>

      {/* Privacy Consent */}
      <div className="flex items-start space-x-3">
        <Checkbox
          id="privacy"
          checked={watchedPrivacyConsent}
          onCheckedChange={(checked) => setValue('privacyConsent', !!checked)}
          className={errors.privacyConsent ? 'border-red-500' : ''}
          disabled={isSubmitting}
        />
        <Label htmlFor="privacy" className="text-sm text-gray-600 leading-relaxed">
          I agree to the privacy policy and terms of service. I consent to DSF Solutions contacting me
          about this inquiry. <span className="text-red-500">*</span>
        </Label>
      </div>
      {errors.privacyConsent && (
        <p className="text-sm text-red-600 ml-6" role="alert">
          {errors.privacyConsent.message}
        </p>
      )}

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full bg-emerald-500 hover:bg-emerald-600"
        size="lg"
        disabled={isSubmitting}
      >
        {isSubmitting ? 'SENDING MESSAGE...' : 'SEND MESSAGE'}
      </Button>
    </form>
  )
}
