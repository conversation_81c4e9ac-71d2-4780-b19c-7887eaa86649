'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Menu } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { useScrolled, useScrollTo } from '@/hooks/use-scroll'
import { NAVIGATION_ITEMS, APP_CONFIG } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface HeaderProps {
  currentPage?: string
  className?: string
}

export function Header({ currentPage = 'home', className }: HeaderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const isScrolled = useScrolled(50)
  const { scrollToElement } = useScrollTo()

  const handleNavigation = (item: typeof NAVIGATION_ITEMS[0]) => {
    if (item.href.startsWith('#')) {
      // Scroll to section
      scrollToElement(item.id, 80)
    } else {
      // Navigate to page
      window.location.href = item.href
    }
    setIsOpen(false)
  }

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-200',
        isScrolled ? 'bg-white/70 backdrop-blur-md shadow-sm' : 'bg-transparent',
        className
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo Section */}
          <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
            <Image
              src="/dsf-logo.png"
              alt={`${APP_CONFIG.name} Logo`}
              width={32}
              height={32}
              className="w-7 h-7 sm:w-8 sm:h-8"
              priority
            />
            <div className="text-lg sm:text-xl lg:text-2xl font-bold tracking-tight">
              {APP_CONFIG.name.split(' ')[0]}{' '}
              <span className="text-emerald-500">{APP_CONFIG.name.split(' ')[1]}</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6 2xl:space-x-8">
            {NAVIGATION_ITEMS.map((item) => (
              <Button
                key={item.name}
                variant="ghost"
                onClick={() => handleNavigation(item)}
                className={cn(
                  'text-xs xl:text-sm font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2 xl:px-3',
                  item.id === currentPage ? 'text-emerald-500' : ''
                )}
              >
                {item.name}
              </Button>
            ))}
          </nav>

          {/* Tablet Navigation */}
          <nav className="hidden md:flex lg:hidden items-center space-x-2">
            {NAVIGATION_ITEMS.slice(0, 4).map((item) => (
              <Button
                key={item.name}
                variant="ghost"
                onClick={() => handleNavigation(item)}
                className={cn(
                  'text-xs font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2',
                  item.id === currentPage ? 'text-emerald-500' : ''
                )}
              >
                {item.name}
              </Button>
            ))}
            
            {/* More button for remaining items */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="text-xs font-medium tracking-wider px-2">
                  MORE
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px]">
                <nav className="flex flex-col space-y-4 mt-8">
                  {NAVIGATION_ITEMS.slice(4).map((item) => (
                    <Button
                      key={item.name}
                      variant="ghost"
                      onClick={() => handleNavigation(item)}
                      className={cn(
                        'justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer',
                        item.id === currentPage ? 'text-emerald-500' : ''
                      )}
                    >
                      {item.name}
                    </Button>
                  ))}
                </nav>
              </SheetContent>
            </Sheet>
          </nav>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu size={24} />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] sm:w-[320px]">
              <nav className="flex flex-col space-y-4 mt-8">
                {NAVIGATION_ITEMS.map((item) => (
                  <Button
                    key={item.name}
                    variant="ghost"
                    onClick={() => handleNavigation(item)}
                    className={cn(
                      'justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer',
                      item.id === currentPage ? 'text-emerald-500' : ''
                    )}
                  >
                    {item.name}
                  </Button>
                ))}
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
