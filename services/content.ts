import { 
  Service, 
  PortfolioItem, 
  BlogPost, 
  Testimonial, 
  BlogCategory, 
  PortfolioCategory,
  PaginatedResponse 
} from '@/types'
import { services } from '@/data/services'
import { portfolioItems } from '@/data/portfolio'
import { blogPosts } from '@/data/blog'
import { testimonials } from '@/data/testimonials'
import { sleep } from '@/lib/utils'

// Simulate API delay for realistic behavior
const SIMULATED_DELAY = 300

/**
 * Content Service
 * Provides a unified interface for accessing all content data
 * In a real application, this would interface with a CMS or API
 */
export class ContentService {
  // Services
  static async getServices(): Promise<Service[]> {
    await sleep(SIMULATED_DELAY)
    return services
  }

  static async getServiceById(id: string): Promise<Service | null> {
    await sleep(SIMULATED_DELAY)
    return services.find(service => service.id === id) || null
  }

  static async getFeaturedServices(): Promise<Service[]> {
    await sleep(SIMULATED_DELAY)
    return services.slice(0, 3)
  }

  // Portfolio
  static async getPortfolioItems(params?: {
    category?: PortfolioCategory
    featured?: boolean
    limit?: number
    page?: number
  }): Promise<PaginatedResponse<PortfolioItem>> {
    await sleep(SIMULATED_DELAY)
    
    let filteredItems = [...portfolioItems]
    
    if (params?.category) {
      filteredItems = filteredItems.filter(item => item.category === params.category)
    }
    
    if (params?.featured !== undefined) {
      filteredItems = filteredItems.filter(item => item.featured === params.featured)
    }

    // Sort by completion date (newest first)
    filteredItems.sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime())

    const page = params?.page || 1
    const limit = params?.limit || 12
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    
    const paginatedItems = filteredItems.slice(startIndex, endIndex)
    
    return {
      data: paginatedItems,
      pagination: {
        page,
        limit,
        total: filteredItems.length,
        totalPages: Math.ceil(filteredItems.length / limit),
      },
    }
  }

  static async getPortfolioItemById(id: string): Promise<PortfolioItem | null> {
    await sleep(SIMULATED_DELAY)
    return portfolioItems.find(item => item.id === id) || null
  }

  static async getFeaturedPortfolioItems(): Promise<PortfolioItem[]> {
    await sleep(SIMULATED_DELAY)
    return portfolioItems.filter(item => item.featured)
  }

  // Blog Posts
  static async getBlogPosts(params?: {
    category?: BlogCategory
    featured?: boolean
    limit?: number
    page?: number
    search?: string
  }): Promise<PaginatedResponse<BlogPost>> {
    await sleep(SIMULATED_DELAY)
    
    let filteredPosts = [...blogPosts]
    
    if (params?.search) {
      const query = params.search.toLowerCase()
      filteredPosts = filteredPosts.filter(
        post =>
          post.title.toLowerCase().includes(query) ||
          post.excerpt.toLowerCase().includes(query) ||
          post.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    if (params?.category) {
      filteredPosts = filteredPosts.filter(post => post.category === params.category)
    }
    
    if (params?.featured !== undefined) {
      filteredPosts = filteredPosts.filter(post => post.featured === params.featured)
    }

    // Sort by publication date (newest first)
    filteredPosts.sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime())

    const page = params?.page || 1
    const limit = params?.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex)
    
    return {
      data: paginatedPosts,
      pagination: {
        page,
        limit,
        total: filteredPosts.length,
        totalPages: Math.ceil(filteredPosts.length / limit),
      },
    }
  }

  static async getBlogPostById(id: string): Promise<BlogPost | null> {
    await sleep(SIMULATED_DELAY)
    return blogPosts.find(post => post.id === id) || null
  }

  static async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    await sleep(SIMULATED_DELAY)
    return blogPosts.find(post => post.slug === slug) || null
  }

  static async getFeaturedBlogPosts(): Promise<BlogPost[]> {
    await sleep(SIMULATED_DELAY)
    return blogPosts.filter(post => post.featured)
  }

  static async getRelatedBlogPosts(postId: string, limit: number = 3): Promise<BlogPost[]> {
    await sleep(SIMULATED_DELAY)
    
    const currentPost = blogPosts.find(post => post.id === postId)
    if (!currentPost) return []

    return blogPosts
      .filter(post => post.id !== postId)
      .filter(post => 
        post.category === currentPost.category ||
        post.tags.some(tag => currentPost.tags.includes(tag))
      )
      .slice(0, limit)
  }

  // Testimonials
  static async getTestimonials(): Promise<Testimonial[]> {
    await sleep(SIMULATED_DELAY)
    return testimonials
  }

  static async getFeaturedTestimonials(): Promise<Testimonial[]> {
    await sleep(SIMULATED_DELAY)
    return testimonials.filter(testimonial => testimonial.featured)
  }

  static async getTestimonialById(id: string): Promise<Testimonial | null> {
    await sleep(SIMULATED_DELAY)
    return testimonials.find(testimonial => testimonial.id === id) || null
  }

  // Search
  static async searchContent(query: string): Promise<{
    services: Service[]
    portfolioItems: PortfolioItem[]
    blogPosts: BlogPost[]
  }> {
    await sleep(SIMULATED_DELAY)
    
    const lowercaseQuery = query.toLowerCase()
    
    const matchingServices = services.filter(
      service =>
        service.title.toLowerCase().includes(lowercaseQuery) ||
        service.description.toLowerCase().includes(lowercaseQuery) ||
        service.features.some(feature => feature.toLowerCase().includes(lowercaseQuery))
    )
    
    const matchingPortfolioItems = portfolioItems.filter(
      item =>
        item.title.toLowerCase().includes(lowercaseQuery) ||
        item.client.toLowerCase().includes(lowercaseQuery) ||
        item.description.toLowerCase().includes(lowercaseQuery) ||
        item.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
    
    const matchingBlogPosts = blogPosts.filter(
      post =>
        post.title.toLowerCase().includes(lowercaseQuery) ||
        post.excerpt.toLowerCase().includes(lowercaseQuery) ||
        post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
    
    return {
      services: matchingServices,
      portfolioItems: matchingPortfolioItems,
      blogPosts: matchingBlogPosts,
    }
  }

  // Categories
  static async getBlogCategories(): Promise<string[]> {
    await sleep(SIMULATED_DELAY)
    const categories = [...new Set(blogPosts.map(post => post.category))]
    return categories
  }

  static async getPortfolioCategories(): Promise<string[]> {
    await sleep(SIMULATED_DELAY)
    const categories = [...new Set(portfolioItems.map(item => item.category))]
    return categories
  }
}
