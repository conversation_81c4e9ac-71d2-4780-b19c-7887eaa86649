import { Skeleton } from "@/components/ui/skeleton"

export default function Loading() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header Skeleton */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white/70 backdrop-blur-md shadow-sm">
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <Skeleton className="w-7 h-7 sm:w-8 sm:h-8 rounded-full" />
              <Skeleton className="h-6 w-32 sm:w-40" />
            </div>
            <div className="hidden lg:flex items-center space-x-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-4 w-16" />
              ))}
            </div>
            <Skeleton className="w-6 h-6 lg:hidden" />
          </div>
        </div>
      </div>

      {/* Hero Section Skeleton */}
      <div className="min-h-screen flex items-center justify-center pt-24 sm:pt-20 md:pt-16">
        <div className="container mx-auto px-6 sm:px-6 text-center">
          <Skeleton className="h-6 w-48 mx-auto mb-6 sm:mb-8" />
          <div className="space-y-4 mb-6 sm:mb-8">
            <Skeleton className="h-16 sm:h-20 md:h-24 lg:h-28 xl:h-32 w-full max-w-4xl mx-auto" />
            <Skeleton className="h-16 sm:h-20 md:h-24 lg:h-28 xl:h-32 w-full max-w-3xl mx-auto" />
          </div>
          <Skeleton className="h-6 sm:h-8 w-full max-w-3xl mx-auto mb-8 sm:mb-12" />
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Skeleton className="h-12 w-48" />
            <Skeleton className="h-12 w-48" />
          </div>
        </div>
      </div>

      {/* Services Section Skeleton */}
      <div className="py-16 sm:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <Skeleton className="h-6 w-32 mx-auto mb-4" />
            <Skeleton className="h-12 sm:h-16 w-full max-w-2xl mx-auto mb-4 sm:mb-6" />
            <Skeleton className="h-6 w-full max-w-2xl mx-auto" />
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="border border-gray-200 rounded-xl p-6">
                <Skeleton className="w-12 h-12 sm:w-16 sm:h-16 rounded-xl mb-4" />
                <Skeleton className="h-6 w-3/4 mb-3" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6 mb-4" />
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="flex items-center space-x-2">
                      <Skeleton className="w-2 h-2 rounded-full" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Portfolio Section Skeleton */}
      <div className="py-16 sm:py-24 bg-emerald-50/30">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <Skeleton className="h-6 w-32 mx-auto mb-4" />
            <Skeleton className="h-12 sm:h-16 w-full max-w-2xl mx-auto mb-4 sm:mb-6" />
            <Skeleton className="h-6 w-full max-w-2xl mx-auto" />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="bg-white border border-gray-200 rounded-xl overflow-hidden">
                <Skeleton className="w-full aspect-[4/3]" />
                <div className="p-5">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
