'use client'

import { useEffect } from 'react'
import { usePerformanceMonitor, usePerformanceAlert } from '@/hooks/use-performance'
import { PerformanceMetrics } from '@/types'

interface PerformanceMonitorProps {
  reportInterval?: number
  enableAlerts?: boolean
  onMetricsReport?: (metrics: PerformanceMetrics) => void
  onPerformanceAlert?: (alerts: string[]) => void
}

export function PerformanceMonitor({
  reportInterval = 30000, // 30 seconds
  enableAlerts = true,
  onMetricsReport,
  onPerformanceAlert,
}: PerformanceMonitorProps) {
  const { webVitals, pageLoad, memory, network } = usePerformanceMonitor({
    reportInterval,
    onReport: onMetricsReport,
  })

  const alerts = usePerformanceAlert({
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1
  })

  useEffect(() => {
    if (enableAlerts && alerts.length > 0) {
      onPerformanceAlert?.(alerts)
    }
  }, [alerts, enableAlerts, onPerformanceAlert])

  // Only render in development or when explicitly enabled
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      {alerts.length > 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-2">
          <div className="font-bold">Performance Alerts:</div>
          <ul className="text-sm">
            {alerts.map((alert, index) => (
              <li key={index}>• {alert}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

// Development-only performance dashboard
export function PerformanceDashboard() {
  const { webVitals, pageLoad, memory, network } = usePerformanceMonitor()

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono max-w-xs">
      <div className="font-bold mb-2">Performance Metrics</div>

      <div className="space-y-1">
        <div className="text-green-400">Web Vitals:</div>
        <div>LCP: {webVitals.lcp ? `${Math.round(webVitals.lcp)}ms` : 'N/A'}</div>
        <div>FID: {webVitals.fid ? `${Math.round(webVitals.fid)}ms` : 'N/A'}</div>
        <div>CLS: {webVitals.cls ? webVitals.cls.toFixed(3) : 'N/A'}</div>
        <div>FCP: {webVitals.fcp ? `${Math.round(webVitals.fcp)}ms` : 'N/A'}</div>
        <div>TTFB: {webVitals.ttfb ? `${Math.round(webVitals.ttfb)}ms` : 'N/A'}</div>
      </div>

      <div className="mt-2 space-y-1">
        <div className="text-blue-400">Page Load:</div>
        <div>Load: {pageLoad.loadTime ? `${Math.round(pageLoad.loadTime)}ms` : 'N/A'}</div>
        <div>DOM: {pageLoad.domContentLoaded ? `${Math.round(pageLoad.domContentLoaded)}ms` : 'N/A'}</div>
      </div>

      {memory.usedJSHeapSize && (
        <div className="mt-2 space-y-1">
          <div className="text-purple-400">Memory:</div>
          <div>Used: {Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB</div>
          <div>Total: {Math.round((memory.totalJSHeapSize || 0) / 1024 / 1024)}MB</div>
        </div>
      )}

      {network.effectiveType && (
        <div className="mt-2 space-y-1">
          <div className="text-orange-400">Network:</div>
          <div>Type: {network.effectiveType}</div>
          <div>RTT: {network.rtt}ms</div>
          <div>Downlink: {network.downlink}Mbps</div>
        </div>
      )}
    </div>
  )
}
