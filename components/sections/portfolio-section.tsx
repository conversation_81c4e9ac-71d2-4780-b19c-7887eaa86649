'use client'

import { Badge } from '@/components/ui/badge'
import { PortfolioCard } from '@/components/portfolio-card'
import { useFeaturedPortfolioItems } from '@/hooks/use-content'
import { cn } from '@/lib/utils'

interface PortfolioSectionProps {
  className?: string
}

export function PortfolioSection({ className }: PortfolioSectionProps) {
  const { data: portfolioItems, loading, error } = useFeaturedPortfolioItems()

  const handleViewProject = (id: string, title: string, client: string) => {
    console.log(`Viewing project: ${title} for ${client}`)
    // In a real application, this would navigate to the project detail page
    // or open a modal with project details
  }

  if (loading) {
    return (
      <section className={cn('py-16 sm:py-24 bg-emerald-50/30', className)}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <div className="h-6 bg-gray-200 rounded w-32 mx-auto mb-4 animate-pulse"></div>
            <div className="h-12 bg-gray-200 rounded w-80 mx-auto mb-6 animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white rounded-xl overflow-hidden shadow-sm animate-pulse">
                <div className="aspect-[4/3] bg-gray-200"></div>
                <div className="p-5">
                  <div className="h-6 bg-gray-200 rounded w-32 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className={cn('py-16 sm:py-24 bg-emerald-50/30', className)}>
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <p className="text-red-600">Failed to load portfolio items. Please try again later.</p>
        </div>
      </section>
    )
  }

  return (
    <section id="portfolio" className={cn('py-16 sm:py-24 bg-emerald-50/30', className)}>
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-20">
          <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
            Featured Work
          </Badge>
          <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
            OUR <span className="text-emerald-500">PORTFOLIO</span>
          </h2>
          <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            A showcase of our latest projects and creative solutions that drive business growth.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {portfolioItems?.map((item) => (
            <div key={item.id} className="h-full">
              <PortfolioCard
                title={item.title}
                client={item.client}
                category={item.category}
                image={item.image}
                onViewProject={() => handleViewProject(item.id, item.title, item.client)}
              />
            </div>
          ))}
        </div>

        {portfolioItems && portfolioItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600">No portfolio items available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  )
}
