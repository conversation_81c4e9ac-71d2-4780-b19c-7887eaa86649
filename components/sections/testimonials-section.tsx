'use client'

import { useState, useCallback } from 'react'
import Image from 'next/image'
import { ChevronLeft, ChevronRight, Star } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useFeaturedTestimonials } from '@/hooks/use-content'
import { cn } from '@/lib/utils'

interface TestimonialsSectionProps {
  className?: string
}

export function TestimonialsSection({ className }: TestimonialsSectionProps) {
  const { data: testimonials, loading, error } = useFeaturedTestimonials()
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const nextTestimonial = useCallback(() => {
    if (testimonials) {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }
  }, [testimonials])

  const prevTestimonial = useCallback(() => {
    if (testimonials) {
      setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    }
  }, [testimonials])

  if (loading) {
    return (
      <section className={cn('py-16 sm:py-24 bg-emerald-600 text-white', className)}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <div className="h-6 bg-white/20 rounded w-48 mx-auto mb-4 animate-pulse"></div>
            <div className="h-12 bg-white/20 rounded w-80 mx-auto animate-pulse"></div>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/10 rounded-xl p-8 animate-pulse">
              <div className="text-center">
                <div className="flex justify-center mb-6 space-x-1">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="w-6 h-6 bg-white/20 rounded"></div>
                  ))}
                </div>
                <div className="h-8 bg-white/20 rounded w-full mb-8"></div>
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-14 h-14 bg-white/20 rounded-full"></div>
                  <div>
                    <div className="h-4 bg-white/20 rounded w-32 mb-2"></div>
                    <div className="h-4 bg-white/20 rounded w-24"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (error || !testimonials || testimonials.length === 0) {
    return (
      <section className={cn('py-16 sm:py-24 bg-emerald-600 text-white', className)}>
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <p className="text-emerald-200">
            {error ? 'Failed to load testimonials.' : 'No testimonials available at the moment.'}
          </p>
        </div>
      </section>
    )
  }

  const currentTestimonialData = testimonials[currentTestimonial]

  return (
    <section id="testimonials" className={cn('py-16 sm:py-24 bg-emerald-600 text-white', className)}>
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center mb-12 sm:mb-20">
          <Badge variant="secondary" className="mb-4 bg-white/20 text-white border-white/30 hover:bg-white/20">
            Client Success Stories
          </Badge>
          <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
            TRUSTED BY <span className="text-emerald-200">LEADERS</span>
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <Card className="bg-white/10 border-white/20 backdrop-blur-sm text-white">
            <CardContent className="p-6 sm:p-8">
              <div className="text-center">
                <div className="flex justify-center mb-4 sm:mb-6">
                  {[...Array(currentTestimonialData.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-200 fill-current" />
                  ))}
                </div>

                <blockquote className="text-xl sm:text-2xl md:text-3xl font-light italic leading-relaxed mb-6 sm:mb-8">
                  "{currentTestimonialData.quote}"
                </blockquote>

                <div className="flex items-center justify-center space-x-4">
                  <Image
                    src={currentTestimonialData.image || '/placeholder.svg'}
                    alt={currentTestimonialData.author.name}
                    width={60}
                    height={60}
                    className="w-12 h-12 sm:w-14 sm:h-14 rounded-full border-2 border-white/30"
                  />
                  <div className="text-left">
                    <div className="font-semibold text-base sm:text-lg">
                      {currentTestimonialData.author.name}
                    </div>
                    <div className="text-emerald-200 text-sm sm:text-base">
                      {currentTestimonialData.company}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center mt-8">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={prevTestimonial}
                  className="text-white hover:bg-white/10"
                  disabled={testimonials.length <= 1}
                >
                  <ChevronLeft size={24} />
                  <span className="sr-only">Previous testimonial</span>
                </Button>

                <div className="flex space-x-2">
                  {testimonials.map((_, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => setCurrentTestimonial(index)}
                      className={cn(
                        'w-2.5 h-2.5 p-0 rounded-full transition-colors',
                        index === currentTestimonial ? 'bg-emerald-200' : 'bg-white/30'
                      )}
                    >
                      <span className="sr-only">Go to testimonial {index + 1}</span>
                    </Button>
                  ))}
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={nextTestimonial}
                  className="text-white hover:bg-white/10"
                  disabled={testimonials.length <= 1}
                >
                  <ChevronRight size={24} />
                  <span className="sr-only">Next testimonial</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
