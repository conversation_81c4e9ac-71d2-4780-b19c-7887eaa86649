# DSF Solutions Website

A modern, high-performance website built with Next.js 15, TypeScript, and Tailwind CSS. This project showcases a comprehensive rewrite implementing modern software architecture patterns, accessibility standards, and performance optimization.

## 🚀 Features

### ✨ Modern Architecture
- **Layered Architecture** with Domain-Driven Design principles
- **Component-based design** following Atomic Design patterns
- **Type-safe** development with TypeScript
- **Error boundaries** for robust error handling
- **Performance monitoring** with Core Web Vitals tracking

### 🎨 UI/UX Excellence
- **Responsive design** with mobile-first approach
- **Accessibility compliant** (WCAG 2.1 AA)
- **Smooth animations** with Framer Motion
- **Modern design system** with shadcn/ui components
- **Dark/light theme support**

### 🔧 Developer Experience
- **ESLint** with TypeScript rules
- **Prettier** for code formatting
- **Husky** for git hooks
- **lint-staged** for pre-commit checks
- **Comprehensive testing** with Jest and Playwright

### ⚡ Performance
- **Core Web Vitals** monitoring
- **Image optimization** with Next.js Image
- **Code splitting** and lazy loading
- **Bundle analysis** and optimization
- **SEO optimized** with proper meta tags

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui + Radix UI
- **Animations**: Framer Motion
- **Forms**: React Hook Form + Zod validation
- **State Management**: Zustand
- **Testing**: Jest + React Testing Library + Playwright
- **Code Quality**: ESLint + Prettier + Husky

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── blog/              # Blog pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # React components
│   ├── forms/             # Form components
│   ├── layout/            # Layout components
│   ├── sections/          # Page sections
│   └── ui/                # Base UI components
├── data/                  # Static data
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and configurations
├── services/              # Application services
├── types/                 # TypeScript type definitions
├── __tests__/             # Unit tests
└── e2e/                   # End-to-end tests
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd dsf-website
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run test:e2e` - Run end-to-end tests
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking

## 🧪 Testing

### Unit Tests
```bash
npm run test
```

### End-to-End Tests
```bash
npm run test:e2e
```

### Coverage Report
```bash
npm run test:coverage
```

## 🏗️ Architecture

### Layered Architecture

The project follows a layered architecture pattern:

1. **Presentation Layer**: React components, pages, and UI logic
2. **Application Layer**: Services, state management, and business logic
3. **Domain Layer**: Types, entities, and domain models
4. **Infrastructure Layer**: External APIs, data sources, and utilities

### Component Architecture

- **Atomic Design**: Components are organized as Atoms → Molecules → Organisms
- **Compound Components**: Complex UI patterns use compound component patterns
- **Custom Hooks**: Logic is extracted into reusable custom hooks
- **Error Boundaries**: Comprehensive error handling at component level

## 🎯 Performance

### Core Web Vitals Targets

- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### Optimization Techniques

- Image optimization with Next.js Image
- Code splitting and lazy loading
- Bundle size optimization
- Performance monitoring and alerts

## ♿ Accessibility

- WCAG 2.1 AA compliance
- Semantic HTML structure
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## 🔒 Security

- Input validation with Zod schemas
- XSS protection
- CSRF protection
- Secure headers configuration
- Content Security Policy

## 📊 Monitoring

- Core Web Vitals tracking
- Error boundary reporting
- Performance metrics collection
- User interaction analytics

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

### Manual Deployment

1. Build the project:
```bash
npm run build
```

2. Start the production server:
```bash
npm run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing framework
- [shadcn/ui](https://ui.shadcn.com/) for the beautiful components
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS
- [Framer Motion](https://www.framer.com/motion/) for smooth animations
