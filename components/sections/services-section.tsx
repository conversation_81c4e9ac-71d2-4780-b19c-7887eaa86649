'use client'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Code, Palette, Smartphone, Target } from 'lucide-react'
import { useFeaturedServices } from '@/hooks/use-content'
import { cn } from '@/lib/utils'

interface ServicesSectionProps {
  className?: string
}

const iconMap = {
  Code,
  Palette,
  Smartphone,
  Target,
}

export function ServicesSection({ className }: ServicesSectionProps) {
  const { data: services, loading, error } = useFeaturedServices()

  if (loading) {
    return (
      <section className={cn('py-16 sm:py-24 bg-white', className)}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <div className="h-6 bg-gray-200 rounded w-32 mx-auto mb-4 animate-pulse"></div>
            <div className="h-12 bg-gray-200 rounded w-96 mx-auto mb-6 animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded w-80 mx-auto animate-pulse"></div>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-6 animate-pulse">
                <div className="w-16 h-16 bg-gray-200 rounded-xl mb-4"></div>
                <div className="h-6 bg-gray-200 rounded w-32 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
                <div className="space-y-2">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded w-24"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className={cn('py-16 sm:py-24 bg-white', className)}>
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <p className="text-red-600">Failed to load services. Please try again later.</p>
        </div>
      </section>
    )
  }

  return (
    <section 
      id="services" 
      className={cn('py-16 sm:py-24 bg-white', className)} 
      aria-labelledby="services-heading"
    >
      <div className="container mx-auto px-4 sm:px-6">
        <header className="text-center mb-12 sm:mb-20">
          <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
            Our Expertise
          </Badge>
          <h2 id="services-heading" className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
            WHAT WE <span className="text-emerald-500">CREATE</span>
          </h2>
          <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            We craft digital experiences that drive results and inspire action through innovative design and
            development.
          </p>
        </header>

        <div className="grid gap-8 md:grid-cols-3" role="list">
          {services?.map((service) => {
            const IconComponent = iconMap[service.icon as keyof typeof iconMap] || Code
            
            return (
              <Card 
                key={service.id} 
                className="group hover:border-emerald-200 hover:shadow-xl transition-all duration-300" 
                role="listitem"
              >
                <CardHeader>
                  <div 
                    className="w-12 h-12 sm:w-16 sm:h-16 bg-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300" 
                    aria-hidden="true"
                  >
                    <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight">
                    {service.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600 leading-relaxed">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2" aria-label={`${service.title} features`}>
                    {service.features.map((feature, featureIndex) => (
                      <li key={`${service.id}-feature-${featureIndex}`} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full" aria-hidden="true"></div>
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
