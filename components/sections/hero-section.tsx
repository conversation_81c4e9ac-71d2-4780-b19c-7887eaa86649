'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FlipWords } from '@/components/ui/flip-words'
import ParticleBackground from '@/components/particle-background'
import { useScrollTo } from '@/hooks/use-scroll'
import { APP_CONFIG } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface HeroSectionProps {
  className?: string
}

export function HeroSection({ className }: HeroSectionProps) {
  const { scrollToElement } = useScrollTo()

  const flipWords = ['creativity.', 'strategy.', 'innovation.']

  return (
    <section
      id="home"
      className={cn(
        'min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-emerald-50/50 to-white pt-24 sm:pt-20 md:pt-16',
        className
      )}
      aria-label="Hero section"
    >
      <ParticleBackground />

      {/* Background Decorations */}
      <div className="absolute inset-0 opacity-10" style={{ zIndex: 2 }} aria-hidden="true">
        <div className="absolute top-1/4 left-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-emerald-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-emerald-600 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 sm:px-6 text-center relative z-10">
        <Badge 
          variant="secondary" 
          className="mb-6 sm:mb-8 bg-emerald-100 text-emerald-700 hover:bg-emerald-100"
        >
          {APP_CONFIG.tagline}
        </Badge>

        <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black tracking-tight leading-[0.9] sm:leading-none mb-6 sm:mb-8 px-2 sm:px-0">
          CRAFTING
          <br />
          <span className="text-emerald-500">DIGITAL</span>
          <br />
          <span className="block sm:inline sm:ml-3">EXPERIENCES</span>
        </h1>

        <div className="flex justify-center items-center mb-8 sm:mb-12">
          <div className="text-lg sm:text-xl md:text-2xl font-light leading-relaxed max-w-3xl mx-auto text-gray-600 px-4 sm:px-0">
            Empowering your digital presence through{' '}
            <FlipWords words={flipWords} />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center px-4 sm:px-0">
          <Button
            size="lg"
            onClick={() => scrollToElement('contact')}
            className="bg-emerald-500 hover:bg-emerald-600 px-8 py-6 text-base sm:text-lg font-semibold tracking-wide"
          >
            START YOUR PROJECT
          </Button>
          <Button
            variant="outline"
            size="lg"
            onClick={() => scrollToElement('portfolio')}
            className="border-2 border-emerald-200 text-emerald-700 hover:bg-emerald-50 px-8 py-6 text-base sm:text-lg font-semibold tracking-wide"
          >
            VIEW OUR WORK
          </Button>
        </div>
      </div>
    </section>
  )
}
