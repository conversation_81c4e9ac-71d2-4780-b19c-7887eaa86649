import { NavigationItem, ServiceCategory, BudgetRange } from '@/types'

// Application Constants
export const APP_CONFIG = {
  name: 'DSF Solutions',
  tagline: 'Digital Excellence Redefined',
  description: 'Empowering businesses through innovative digital solutions, creative design, and strategic thinking.',
  url: 'https://dsfsolutions.com',
  email: '<EMAIL>',
  phone: '+****************',
  address: {
    street: '123 Design Street',
    city: 'Creative District',
    state: 'CD',
    zip: '12345',
  },
  social: {
    twitter: '@dsfsolutions',
    linkedin: 'dsf-solutions',
    instagram: 'dsfsolutions',
    dribbble: 'dsfsolutions',
  },
} as const

// Navigation Configuration
export const NAVIGATION_ITEMS: NavigationItem[] = [
  { name: 'HOME', href: '/', id: 'home' },
  { name: 'SERVICES', href: '#services', id: 'services' },
  { name: 'PORTFOL<PERSON>', href: '#portfolio', id: 'portfolio' },
  { name: 'BLOG', href: '/blog', id: 'blog' },
  { name: 'ABOUT', href: '#about', id: 'about' },
  { name: 'CONTACT', href: '#contact', id: 'contact' },
] as const

// Service Categories
export const SERVICE_CATEGORIES = [
  { value: ServiceCategory.WEB_DEVELOPMENT, label: 'Web Development' },
  { value: ServiceCategory.BRAND_IDENTITY, label: 'Brand Identity' },
  { value: ServiceCategory.UI_UX_DESIGN, label: 'UI/UX Design' },
  { value: ServiceCategory.CONSULTATION, label: 'Consultation' },
] as const

// Budget Ranges
export const BUDGET_RANGES = [
  { value: BudgetRange.SMALL, label: '$5,000 - $10,000' },
  { value: BudgetRange.MEDIUM, label: '$10,000 - $25,000' },
  { value: BudgetRange.LARGE, label: '$25,000 - $50,000' },
  { value: BudgetRange.ENTERPRISE, label: '$50,000+' },
] as const

// Animation Constants
export const ANIMATION_CONFIG = {
  duration: {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
  },
  easing: {
    easeOut: [0.0, 0.0, 0.2, 1],
    easeIn: [0.4, 0.0, 1, 1],
    easeInOut: [0.4, 0.0, 0.2, 1],
  },
} as const

// Performance Constants
export const PERFORMANCE_CONFIG = {
  particleCount: {
    mobile: 30,
    tablet: 50,
    desktop: 80,
  },
  connectionDistance: {
    mobile: 80,
    tablet: 100,
    desktop: 120,
  },
  imageOptimization: {
    quality: 85,
    formats: ['webp', 'avif'],
  },
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

// SEO Constants
export const SEO_CONFIG = {
  defaultTitle: `${APP_CONFIG.name} - ${APP_CONFIG.tagline}`,
  titleTemplate: `%s | ${APP_CONFIG.name}`,
  defaultDescription: APP_CONFIG.description,
  keywords: [
    'web development',
    'digital design',
    'branding',
    'UI/UX',
    'creative agency',
    'digital solutions',
    'responsive design',
    'modern websites',
  ],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: APP_CONFIG.url,
    siteName: APP_CONFIG.name,
    images: [
      {
        url: `${APP_CONFIG.url}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: `${APP_CONFIG.name} - ${APP_CONFIG.tagline}`,
      },
    ],
  },
  twitter: {
    handle: APP_CONFIG.social.twitter,
    site: APP_CONFIG.social.twitter,
    cardType: 'summary_large_image',
  },
} as const

// Form Validation Constants
export const VALIDATION_CONFIG = {
  name: {
    minLength: 2,
    maxLength: 50,
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  message: {
    minLength: 10,
    maxLength: 1000,
  },
  company: {
    maxLength: 100,
  },
} as const

// Error Messages
export const ERROR_MESSAGES = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Must be no more than ${max} characters`,
  generic: 'Something went wrong. Please try again.',
  network: 'Network error. Please check your connection.',
  server: 'Server error. Please try again later.',
} as const
