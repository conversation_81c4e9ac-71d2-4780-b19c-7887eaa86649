import { ContentService } from '@/services/content'
import { ServiceCategory, PortfolioCategory, BlogCategory } from '@/types'

// Mock the data imports
jest.mock('@/data/services', () => ({
  services: [
    {
      id: 'web-development',
      title: 'Web Development',
      description: 'Test description',
      features: ['Feature 1', 'Feature 2'],
      icon: 'Code',
      category: 'web-development',
    },
    {
      id: 'ui-ux-design',
      title: 'UI/UX Design',
      description: 'Test description',
      features: ['Feature 1', 'Feature 2'],
      icon: 'Smartphone',
      category: 'ui-ux-design',
    },
  ],
}))

jest.mock('@/data/portfolio', () => ({
  portfolioItems: [
    {
      id: 'project-1',
      title: 'Project 1',
      client: 'Client 1',
      category: 'web-development',
      description: 'Test description',
      image: 'test-image.jpg',
      tags: ['React', 'TypeScript'],
      featured: true,
      completedAt: new Date('2024-01-15'),
    },
    {
      id: 'project-2',
      title: 'Project 2',
      client: 'Client 2',
      category: 'branding',
      description: 'Test description',
      image: 'test-image-2.jpg',
      tags: ['Design', 'Branding'],
      featured: false,
      completedAt: new Date('2024-01-10'),
    },
  ],
}))

jest.mock('@/data/blog', () => ({
  blogPosts: [
    {
      id: 'post-1',
      title: 'Test Post 1',
      excerpt: 'Test excerpt',
      content: 'Test content',
      author: { id: 'author-1', name: 'Author 1', email: '<EMAIL>' },
      publishedAt: new Date('2024-01-15'),
      readTime: '5 min read',
      category: 'design',
      tags: ['Design', 'UI'],
      image: 'test-image.jpg',
      featured: true,
      slug: 'test-post-1',
    },
    {
      id: 'post-2',
      title: 'Test Post 2',
      excerpt: 'Test excerpt 2',
      content: 'Test content 2',
      author: { id: 'author-2', name: 'Author 2', email: '<EMAIL>' },
      publishedAt: new Date('2024-01-10'),
      readTime: '3 min read',
      category: 'development',
      tags: ['Development', 'React'],
      image: 'test-image-2.jpg',
      featured: false,
      slug: 'test-post-2',
    },
  ],
}))

jest.mock('@/data/testimonials', () => ({
  testimonials: [
    {
      id: 'testimonial-1',
      quote: 'Great work!',
      author: { id: 'client-1', name: 'Client 1', email: '<EMAIL>' },
      company: 'Company 1',
      rating: 5,
      featured: true,
    },
    {
      id: 'testimonial-2',
      quote: 'Excellent service!',
      author: { id: 'client-2', name: 'Client 2', email: '<EMAIL>' },
      company: 'Company 2',
      rating: 5,
      featured: false,
    },
  ],
}))

describe('ContentService', () => {
  describe('Services', () => {
    it('should return all services', async () => {
      const services = await ContentService.getServices()
      expect(services).toHaveLength(2)
      expect(services[0].title).toBe('Web Development')
    })

    it('should return service by id', async () => {
      const service = await ContentService.getServiceById('web-development')
      expect(service).toBeTruthy()
      expect(service?.title).toBe('Web Development')
    })

    it('should return null for non-existent service', async () => {
      const service = await ContentService.getServiceById('non-existent')
      expect(service).toBeNull()
    })

    it('should return featured services', async () => {
      const services = await ContentService.getFeaturedServices()
      expect(services).toHaveLength(2) // First 3, but we only have 2
    })
  })

  describe('Portfolio', () => {
    it('should return paginated portfolio items', async () => {
      const result = await ContentService.getPortfolioItems()
      expect(result.data).toHaveLength(2)
      expect(result.pagination.total).toBe(2)
      expect(result.pagination.page).toBe(1)
    })

    it('should filter portfolio items by category', async () => {
      const result = await ContentService.getPortfolioItems({
        category: PortfolioCategory.WEB_DEVELOPMENT,
      })
      expect(result.data).toHaveLength(1)
      expect(result.data[0].category).toBe('web-development')
    })

    it('should filter portfolio items by featured status', async () => {
      const result = await ContentService.getPortfolioItems({ featured: true })
      expect(result.data).toHaveLength(1)
      expect(result.data[0].featured).toBe(true)
    })

    it('should paginate portfolio items', async () => {
      const result = await ContentService.getPortfolioItems({ limit: 1, page: 1 })
      expect(result.data).toHaveLength(1)
      expect(result.pagination.totalPages).toBe(2)
    })

    it('should return portfolio item by id', async () => {
      const item = await ContentService.getPortfolioItemById('project-1')
      expect(item).toBeTruthy()
      expect(item?.title).toBe('Project 1')
    })

    it('should return featured portfolio items', async () => {
      const items = await ContentService.getFeaturedPortfolioItems()
      expect(items).toHaveLength(1)
      expect(items[0].featured).toBe(true)
    })
  })

  describe('Blog Posts', () => {
    it('should return paginated blog posts', async () => {
      const result = await ContentService.getBlogPosts()
      expect(result.data).toHaveLength(2)
      expect(result.pagination.total).toBe(2)
    })

    it('should filter blog posts by category', async () => {
      const result = await ContentService.getBlogPosts({
        category: BlogCategory.DESIGN,
      })
      expect(result.data).toHaveLength(1)
      expect(result.data[0].category).toBe('design')
    })

    it('should search blog posts', async () => {
      const result = await ContentService.getBlogPosts({ search: 'React' })
      expect(result.data).toHaveLength(1)
      expect(result.data[0].tags).toContain('React')
    })

    it('should return blog post by id', async () => {
      const post = await ContentService.getBlogPostById('post-1')
      expect(post).toBeTruthy()
      expect(post?.title).toBe('Test Post 1')
    })

    it('should return blog post by slug', async () => {
      const post = await ContentService.getBlogPostBySlug('test-post-1')
      expect(post).toBeTruthy()
      expect(post?.title).toBe('Test Post 1')
    })

    it('should return featured blog posts', async () => {
      const posts = await ContentService.getFeaturedBlogPosts()
      expect(posts).toHaveLength(1)
      expect(posts[0].featured).toBe(true)
    })

    it('should return related blog posts', async () => {
      const posts = await ContentService.getRelatedBlogPosts('post-1')
      expect(posts).toHaveLength(1)
      expect(posts[0].id).toBe('post-2')
    })
  })

  describe('Testimonials', () => {
    it('should return all testimonials', async () => {
      const testimonials = await ContentService.getTestimonials()
      expect(testimonials).toHaveLength(2)
    })

    it('should return featured testimonials', async () => {
      const testimonials = await ContentService.getFeaturedTestimonials()
      expect(testimonials).toHaveLength(1)
      expect(testimonials[0].featured).toBe(true)
    })

    it('should return testimonial by id', async () => {
      const testimonial = await ContentService.getTestimonialById('testimonial-1')
      expect(testimonial).toBeTruthy()
      expect(testimonial?.quote).toBe('Great work!')
    })
  })

  describe('Search', () => {
    it('should search across all content types', async () => {
      const results = await ContentService.searchContent('React')
      expect(results.portfolioItems).toHaveLength(1)
      expect(results.blogPosts).toHaveLength(1)
      expect(results.services).toHaveLength(0)
    })
  })

  describe('Categories', () => {
    it('should return blog categories', async () => {
      const categories = await ContentService.getBlogCategories()
      expect(categories).toContain('design')
      expect(categories).toContain('development')
    })

    it('should return portfolio categories', async () => {
      const categories = await ContentService.getPortfolioCategories()
      expect(categories).toContain('web-development')
      expect(categories).toContain('branding')
    })
  })
})
