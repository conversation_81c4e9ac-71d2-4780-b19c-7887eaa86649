import { useState, useEffect, useCallback } from 'react'
import { throttle } from '@/lib/utils'

// Hook for tracking scroll position
export function useScrollPosition() {
  const [scrollPosition, setScrollPosition] = useState(0)

  useEffect(() => {
    const updatePosition = throttle(() => {
      setScrollPosition(window.pageYOffset)
    }, 100)

    window.addEventListener('scroll', updatePosition, { passive: true })
    updatePosition() // Set initial position

    return () => window.removeEventListener('scroll', updatePosition)
  }, [])

  return scrollPosition
}

// Hook for detecting if user has scrolled past a certain point
export function useScrolled(threshold: number = 50) {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const updateScrolled = throttle(() => {
      setIsScrolled(window.pageYOffset > threshold)
    }, 100)

    window.addEventListener('scroll', updateScrolled, { passive: true })
    updateScrolled() // Set initial state

    return () => window.removeEventListener('scroll', updateScrolled)
  }, [threshold])

  return isScrolled
}

// Hook for smooth scrolling to elements
export function useScrollTo() {
  const scrollToElement = useCallback((elementId: string, offset: number = 0) => {
    const element = document.getElementById(elementId)
    if (element) {
      const elementPosition = element.offsetTop - offset
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth',
      })
    }
  }, [])

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }, [])

  return { scrollToElement, scrollToTop }
}

// Hook for detecting scroll direction
export function useScrollDirection() {
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null)
  const [lastScrollY, setLastScrollY] = useState(0)

  useEffect(() => {
    const updateScrollDirection = throttle(() => {
      const scrollY = window.pageYOffset
      const direction = scrollY > lastScrollY ? 'down' : 'up'
      
      if (direction !== scrollDirection && (scrollY - lastScrollY > 10 || scrollY - lastScrollY < -10)) {
        setScrollDirection(direction)
      }
      
      setLastScrollY(scrollY > 0 ? scrollY : 0)
    }, 100)

    window.addEventListener('scroll', updateScrollDirection, { passive: true })
    
    return () => window.removeEventListener('scroll', updateScrollDirection)
  }, [scrollDirection, lastScrollY])

  return scrollDirection
}

// Hook for intersection observer (element visibility)
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options?: IntersectionObserverInit
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
        ...options,
      }
    )

    observer.observe(element)

    return () => observer.unobserve(element)
  }, [elementRef, options, hasIntersected])

  return { isIntersecting, hasIntersected }
}

// Hook for scroll-triggered animations
export function useScrollAnimation(threshold: number = 0.1) {
  const [ref, setRef] = useState<Element | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (!ref) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.unobserve(ref) // Only trigger once
        }
      },
      {
        threshold,
        rootMargin: '0px 0px -100px 0px',
      }
    )

    observer.observe(ref)

    return () => observer.unobserve(ref)
  }, [ref, threshold])

  return [setRef, isVisible] as const
}

// Hook for parallax scrolling effect
export function useParallax(speed: number = 0.5) {
  const [offset, setOffset] = useState(0)

  useEffect(() => {
    const updateOffset = throttle(() => {
      setOffset(window.pageYOffset * speed)
    }, 16) // ~60fps

    window.addEventListener('scroll', updateOffset, { passive: true })
    
    return () => window.removeEventListener('scroll', updateOffset)
  }, [speed])

  return offset
}

// Hook for reading progress indicator
export function useReadingProgress() {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const updateProgress = throttle(() => {
      const scrollTop = window.pageYOffset
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercent = (scrollTop / docHeight) * 100
      
      setProgress(Math.min(100, Math.max(0, scrollPercent)))
    }, 100)

    window.addEventListener('scroll', updateProgress, { passive: true })
    updateProgress() // Set initial progress

    return () => window.removeEventListener('scroll', updateProgress)
  }, [])

  return progress
}
