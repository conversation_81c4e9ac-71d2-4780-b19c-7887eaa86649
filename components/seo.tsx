import Head from 'next/head'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
}

export function SEO({
  title = 'DSF Solutions - Digital Excellence Redefined',
  description = 'Empowering businesses through innovative digital solutions, creative design, and strategic thinking. We craft digital experiences that drive results.',
  keywords = ['web development', 'digital design', 'branding', 'UI/UX', 'creative agency'],
  image = '/og-image.jpg',
  url = 'https://dsfsolutions.com',
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
}: SEOProps) {
  const fullTitle = title.includes('DSF Solutions') ? title : `${title} | DSF Solutions`
  const fullUrl = url.startsWith('http') ? url : `https://dsfsolutions.com${url}`
  const fullImage = image.startsWith('http') ? image : `https://dsfsolutions.com${image}`

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author || 'DSF Solutions'} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="DSF Solutions" />
      <meta property="og:locale" content="en_US" />
      
      {/* Article specific Open Graph tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && author && (
        <meta property="article:author" content={author} />
      )}
      {type === 'article' && section && (
        <meta property="article:section" content={section} />
      )}
      {type === 'article' && tags && tags.map((tag, index) => (
        <meta key={index} property="article:tag" content={tag} />
      ))}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      <meta name="twitter:creator" content="@dsfsolutions" />
      <meta name="twitter:site" content="@dsfsolutions" />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#10B981" />
      <meta name="msapplication-TileColor" content="#10B981" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: 'DSF Solutions',
            description: description,
            url: 'https://dsfsolutions.com',
            logo: 'https://dsfsolutions.com/dsf-logo.png',
            contactPoint: {
              '@type': 'ContactPoint',
              telephone: '******-123-4567',
              contactType: 'customer service',
              email: '<EMAIL>',
            },
            address: {
              '@type': 'PostalAddress',
              streetAddress: '123 Design Street',
              addressLocality: 'Creative District',
              postalCode: '12345',
              addressCountry: 'US',
            },
            sameAs: [
              'https://twitter.com/dsfsolutions',
              'https://linkedin.com/company/dsfsolutions',
              'https://instagram.com/dsfsolutions',
            ],
          }),
        }}
      />
    </Head>
  )
}
