import { useState, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ContactForm } from '@/types'
import { contactFormSchema, ContactFormData } from '@/lib/validations'
import { ContactService } from '@/services/contact'

interface UseContactFormOptions {
  onSuccess?: (data: ContactFormData) => void
  onError?: (error: string) => void
}

export function useContactForm(options?: UseContactFormOptions) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      company: '',
      message: '',
      privacyConsent: false,
    },
  })

  const onSubmit = useCallback(async (data: ContactFormData) => {
    try {
      setIsSubmitting(true)
      setSubmitError(null)
      setSubmitSuccess(false)

      const response = await ContactService.submitContactForm(data)

      if (response.success) {
        setSubmitSuccess(true)
        form.reset()
        options?.onSuccess?.(data)
      } else {
        setSubmitError(response.message || 'Failed to send message')
        options?.onError?.(response.message || 'Failed to send message')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setSubmitError(errorMessage)
      options?.onError?.(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }, [form, options])

  const clearMessages = useCallback(() => {
    setSubmitError(null)
    setSubmitSuccess(false)
  }, [])

  return {
    form,
    onSubmit: form.handleSubmit(onSubmit),
    isSubmitting,
    submitError,
    submitSuccess,
    clearMessages,
  }
}

// Newsletter subscription hook
export function useNewsletterSubscription() {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const subscribe = useCallback(async (emailAddress?: string) => {
    const emailToSubmit = emailAddress || email

    if (!emailToSubmit) {
      setError('Please enter an email address')
      return
    }

    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(false)

      const response = await ContactService.subscribeToNewsletter(emailToSubmit)

      if (response.success) {
        setSuccess(true)
        setEmail('')
      } else {
        setError(response.message || 'Failed to subscribe')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }, [email])

  const clearMessages = useCallback(() => {
    setError(null)
    setSuccess(false)
  }, [])

  return {
    email,
    setEmail,
    subscribe,
    isSubmitting,
    error,
    success,
    clearMessages,
  }
}

// Project inquiry hook
export function useProjectInquiry() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const submitInquiry = useCallback(async (data: {
    name: string
    email: string
    company?: string
    projectType: string
    budget: string
    timeline: string
    description: string
  }) => {
    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(false)

      const response = await ContactService.sendProjectInquiry(data)

      if (response.success) {
        setSuccess(true)
      } else {
        setError(response.message || 'Failed to send inquiry')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }, [])

  const clearMessages = useCallback(() => {
    setError(null)
    setSuccess(false)
  }, [])

  return {
    submitInquiry,
    isSubmitting,
    error,
    success,
    clearMessages,
  }
}

// Quote request hook
export function useQuoteRequest() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [quoteId, setQuoteId] = useState<string | null>(null)

  const requestQuote = useCallback(async (data: {
    contactInfo: {
      name: string
      email: string
      company?: string
      phone?: string
    }
    projectDetails: {
      type: string
      scope: string[]
      timeline: string
      budget: string
      description: string
    }
    requirements: {
      features: string[]
      integrations: string[]
      platforms: string[]
    }
  }) => {
    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(false)
      setQuoteId(null)

      const response = await ContactService.requestQuote(data)

      if (response.success) {
        setSuccess(true)
        setQuoteId(response.data.quoteId)
      } else {
        setError(response.message || 'Failed to request quote')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }, [])

  const clearMessages = useCallback(() => {
    setError(null)
    setSuccess(false)
    setQuoteId(null)
  }, [])

  return {
    requestQuote,
    isSubmitting,
    error,
    success,
    quoteId,
    clearMessages,
  }
}
