'use client'

import { Mail, Phone, MapPin, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ContactForm } from '@/components/forms/contact-form'
import { APP_CONFIG } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface ContactSectionProps {
  className?: string
}

export function ContactSection({ className }: ContactSectionProps) {
  const contactInfo = [
    {
      icon: Mail,
      label: 'Email',
      value: APP_CONFIG.email,
      href: `mailto:${APP_CONFIG.email}`,
    },
    {
      icon: Phone,
      label: 'Phone',
      value: APP_CONFIG.phone,
      href: `tel:${APP_CONFIG.phone}`,
    },
    {
      icon: MapPin,
      label: 'Address',
      value: `${APP_CONFIG.address.street}\n${APP_CONFIG.address.city}, ${APP_CONFIG.address.state} ${APP_CONFIG.address.zip}`,
    },
  ]

  const officeHours = [
    { day: 'Monday - Friday', hours: '9:00 AM - 6:00 PM' },
    { day: 'Saturday', hours: '10:00 AM - 4:00 PM' },
    { day: 'Sunday', hours: 'Closed' },
  ]

  return (
    <section id="contact" className={cn('py-16 sm:py-24 bg-white', className)}>
      <div className="container mx-auto px-4 sm:px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
              Get In Touch
            </Badge>
            <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
              LET'S <span className="text-emerald-500">COLLABORATE</span>
            </h2>
            <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Ready to transform your digital presence? Let's discuss your project and create something extraordinary
              together.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 sm:gap-16">
            {/* Contact Info */}
            <div className="space-y-6 sm:space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight uppercase flex items-center">
                    <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-500 mr-3" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <info.icon className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{info.label}</p>
                        {info.href ? (
                          <a
                            href={info.href}
                            className="text-gray-600 hover:text-emerald-600 transition-colors"
                          >
                            {info.value}
                          </a>
                        ) : (
                          <p className="text-gray-600 whitespace-pre-line">{info.value}</p>
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="border-t border-gray-200 my-6"></div>

                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-emerald-600" />
                    </div>
                    <div>
                      <p className="font-medium">Office Hours</p>
                      <div className="text-gray-600 space-y-1">
                        {officeHours.map((schedule, index) => (
                          <p key={index}>
                            {schedule.day}: {schedule.hours}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight">
                  Send us a message
                </CardTitle>
                <CardDescription>
                  Fill out the form below and we'll get back to you within 24 hours.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ContactForm />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
