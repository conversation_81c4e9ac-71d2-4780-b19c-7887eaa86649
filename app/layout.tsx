import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import { PerformanceMonitor } from '@/components/performance-monitor'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: {
    default: 'DSF Solutions - Digital Excellence Redefined',
    template: '%s | DSF Solutions'
  },
  description: 'Empowering businesses through innovative digital solutions, creative design, and strategic thinking. We craft digital experiences that drive results.',
  keywords: ['web development', 'digital design', 'branding', 'UI/UX', 'creative agency'],
  authors: [{ name: 'DSF Solutions' }],
  creator: 'DSF Solutions',
  publisher: 'DSF Solutions',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://dsfsolutions.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'DSF Solutions - Digital Excellence Redefined',
    description: 'Empowering businesses through innovative digital solutions, creative design, and strategic thinking.',
    url: 'https://dsfsolutions.com',
    siteName: 'DSF Solutions',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'DSF Solutions - Digital Excellence Redefined',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DSF Solutions - Digital Excellence Redefined',
    description: 'Empowering businesses through innovative digital solutions, creative design, and strategic thinking.',
    images: ['/og-image.jpg'],
    creator: '@dsfsolutions',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#10B981' },
    { media: '(prefers-color-scheme: dark)', color: '#059669' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className={`${inter.className} antialiased`}>
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
