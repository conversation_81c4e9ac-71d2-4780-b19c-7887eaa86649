import { <PERSON><PERSON><PERSON><PERSON>, Author, BlogCategory } from '@/types'
import { calculateReadingTime } from '@/lib/utils'

const authors: Author[] = [
  {
    id: 'sarah-johnson',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Senior UX Designer with 8+ years of experience in creating user-centered digital experiences.',
    socialLinks: {
      twitter: 'https://twitter.com/sarahjohnson',
      linkedin: 'https://linkedin.com/in/sarah<PERSON>hnson',
    },
  },
  {
    id: 'michael-chen',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Full-stack developer specializing in React, Node.js, and modern web technologies.',
    socialLinks: {
      twitter: 'https://twitter.com/michaelchen',
      github: 'https://github.com/michael<PERSON>',
    },
  },
  {
    id: 'emily-rodrig<PERSON><PERSON>',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Brand strategist and visual designer passionate about creating memorable brand experiences.',
    socialLinks: {
      dribbble: 'https://dribbble.com/emilyrodriguez',
      instagram: 'https://instagram.com/emilyrodriguez',
    },
  },
  {
    id: 'david-kim',
    name: 'David Kim',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Digital marketing expert with expertise in SEO, content strategy, and growth hacking.',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/davidkim',
      twitter: 'https://twitter.com/davidkim',
    },
  },
]

const blogContent = {
  webDesignTrends: `
    The digital landscape is constantly evolving, and 2024 brings exciting new trends that are reshaping how we approach web design. From AI-powered personalization to immersive 3D experiences, designers are pushing the boundaries of what's possible on the web.

    ## Key Trends to Watch

    ### 1. AI-Powered Personalization
    Artificial intelligence is revolutionizing how websites adapt to individual users. Dynamic content, personalized layouts, and intelligent recommendations are becoming standard features.

    ### 2. Immersive 3D Experiences
    WebGL and Three.js are making 3D graphics more accessible, allowing designers to create engaging, interactive experiences that were once only possible in native applications.

    ### 3. Sustainable Design
    Environmental consciousness is driving the adoption of sustainable design practices, focusing on performance optimization and reduced carbon footprints.

    ### 4. Voice User Interfaces
    With the rise of voice assistants, designing for voice interactions is becoming increasingly important for accessibility and user convenience.

    ## Implementation Strategies

    To successfully implement these trends, consider:
    - User experience first approach
    - Performance optimization
    - Accessibility compliance
    - Cross-platform compatibility

    The future of web design is bright, with technology enabling more creative and user-friendly experiences than ever before.
  `,
  reactPerformance: `
    Building high-performance React applications requires a deep understanding of React's rendering behavior and optimization techniques. In this comprehensive guide, we'll explore proven strategies to make your React apps lightning-fast.

    ## Understanding React Performance

    React's virtual DOM is efficient, but it's not magic. Understanding when and why components re-render is crucial for optimization.

    ### Common Performance Bottlenecks
    - Unnecessary re-renders
    - Large bundle sizes
    - Inefficient state management
    - Poor component architecture

    ## Optimization Techniques

    ### 1. Memoization
    Use React.memo, useMemo, and useCallback strategically to prevent unnecessary re-renders.

    ### 2. Code Splitting
    Implement dynamic imports and lazy loading to reduce initial bundle size.

    ### 3. State Management
    Choose the right state management solution and structure your state efficiently.

    ### 4. Bundle Analysis
    Regularly analyze your bundle to identify and eliminate bloat.

    ## Best Practices

    - Profile before optimizing
    - Measure performance improvements
    - Consider the user experience impact
    - Maintain code readability

    Remember, premature optimization is the root of all evil. Always measure and profile before making changes.
  `,
}

export const blogPosts: BlogPost[] = [
  {
    id: 'web-design-trends-2024',
    title: 'The Future of Web Design: Trends to Watch in 2024',
    excerpt: 'Explore the cutting-edge design trends that are shaping the digital landscape and how they can transform your brand\'s online presence.',
    content: blogContent.webDesignTrends,
    author: authors[0],
    publishedAt: new Date('2024-01-15'),
    readTime: calculateReadingTime(blogContent.webDesignTrends),
    category: BlogCategory.DESIGN,
    tags: ['Web Design', 'Trends', 'UI/UX', '2024'],
    image: 'https://picsum.photos/800/400?random=1',
    featured: true,
    slug: 'web-design-trends-2024',
  },
  {
    id: 'react-performance-optimization',
    title: 'Building High-Performance React Applications',
    excerpt: 'Learn the best practices and optimization techniques to create lightning-fast React applications that deliver exceptional user experiences.',
    content: blogContent.reactPerformance,
    author: authors[1],
    publishedAt: new Date('2024-01-12'),
    readTime: calculateReadingTime(blogContent.reactPerformance),
    category: BlogCategory.DEVELOPMENT,
    tags: ['React', 'Performance', 'JavaScript', 'Optimization'],
    image: 'https://picsum.photos/800/400?random=2',
    featured: true,
    slug: 'react-performance-optimization',
  },
  {
    id: 'brand-identity-design-guide',
    title: 'Brand Identity Design: Creating Memorable Visual Stories',
    excerpt: 'Discover how to craft compelling brand identities that resonate with your audience and stand out in today\'s competitive market.',
    content: 'Complete guide to brand identity design...',
    author: authors[2],
    publishedAt: new Date('2024-01-10'),
    readTime: '6 min read',
    category: BlogCategory.BRANDING,
    tags: ['Branding', 'Identity', 'Design', 'Visual Strategy'],
    image: 'https://picsum.photos/800/400?random=3',
    featured: false,
    slug: 'brand-identity-design-guide',
  },
  {
    id: 'seo-guide-modern-websites',
    title: 'The Complete Guide to SEO for Modern Websites',
    excerpt: 'Master the art of search engine optimization with our comprehensive guide covering the latest SEO strategies and techniques.',
    content: 'Comprehensive SEO guide...',
    author: authors[3],
    publishedAt: new Date('2024-01-08'),
    readTime: '15 min read',
    category: BlogCategory.MARKETING,
    tags: ['SEO', 'Marketing', 'Web Development', 'Search'],
    image: 'https://picsum.photos/800/400?random=4',
    featured: false,
    slug: 'seo-guide-modern-websites',
  },
  {
    id: 'mobile-first-design-importance',
    title: 'Mobile-First Design: Why It Matters More Than Ever',
    excerpt: 'Understanding the importance of mobile-first design approach and how it impacts user experience and business success.',
    content: 'Mobile-first design principles...',
    author: authors[0],
    publishedAt: new Date('2024-01-05'),
    readTime: '7 min read',
    category: BlogCategory.DESIGN,
    tags: ['Mobile Design', 'UX', 'Responsive', 'Strategy'],
    image: 'https://picsum.photos/800/400?random=5',
    featured: false,
    slug: 'mobile-first-design-importance',
  },
]

export function getBlogPostById(id: string): BlogPost | undefined {
  return blogPosts.find(post => post.id === id)
}

export function getBlogPostBySlug(slug: string): BlogPost | undefined {
  return blogPosts.find(post => post.slug === slug)
}

export function getFeaturedBlogPosts(): BlogPost[] {
  return blogPosts.filter(post => post.featured)
}

export function getBlogPostsByCategory(category: BlogCategory): BlogPost[] {
  return blogPosts.filter(post => post.category === category)
}

export function getBlogPostsByAuthor(authorId: string): BlogPost[] {
  return blogPosts.filter(post => post.author.id === authorId)
}

export function searchBlogPosts(query: string): BlogPost[] {
  const lowercaseQuery = query.toLowerCase()
  return blogPosts.filter(
    post =>
      post.title.toLowerCase().includes(lowercaseQuery) ||
      post.excerpt.toLowerCase().includes(lowercaseQuery) ||
      post.content.toLowerCase().includes(lowercaseQuery) ||
      post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  )
}

export function getRecentBlogPosts(limit: number = 5): BlogPost[] {
  return blogPosts
    .sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime())
    .slice(0, limit)
}

export function getRelatedBlogPosts(postId: string, limit: number = 3): BlogPost[] {
  const currentPost = getBlogPostById(postId)
  if (!currentPost) return []

  return blogPosts
    .filter(post => post.id !== postId)
    .filter(post => 
      post.category === currentPost.category ||
      post.tags.some(tag => currentPost.tags.includes(tag))
    )
    .slice(0, limit)
}
