'use client'

import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useNewsletterSubscription } from '@/hooks/use-contact-form'
import { APP_CONFIG } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface FooterProps {
  className?: string
}

export function Footer({ className }: FooterProps) {
  const {
    email,
    setEmail,
    subscribe,
    isSubmitting,
    error,
    success,
    clearMessages,
  } = useNewsletterSubscription()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    subscribe()
  }

  const quickLinks = [
    { name: 'About Us', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Blog', href: '/blog' },
    { name: 'Contact', href: '#contact' },
    { name: 'Careers', href: '/careers' },
  ]

  const socialLinks = [
    { name: 'Instagram', href: APP_CONFIG.social.instagram },
    { name: 'Twitter', href: `https://twitter.com/${APP_CONFIG.social.twitter.replace('@', '')}` },
    { name: 'LinkedIn', href: `https://linkedin.com/company/${APP_CONFIG.social.linkedin}` },
    { name: 'Dribbble', href: `https://dribbble.com/${APP_CONFIG.social.dribbble}` },
  ]

  const legalLinks = [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
  ]

  return (
    <footer className={cn('bg-emerald-50 py-12 sm:py-16 border-t border-emerald-100', className)}>
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4 sm:mb-6">
              <Image
                src="/dsf-logo.png"
                alt={`${APP_CONFIG.name} Logo`}
                width={32}
                height={32}
                className="w-7 h-7 sm:w-8 sm:h-8"
              />
              <div className="text-xl sm:text-2xl font-bold tracking-tight">
                {APP_CONFIG.name.split(' ')[0]}{' '}
                <span className="text-emerald-500">{APP_CONFIG.name.split(' ')[1]}</span>
              </div>
            </div>
            <p className="text-gray-600 mb-6 max-w-md">
              {APP_CONFIG.description}
            </p>
            <div className="flex flex-wrap gap-4">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="link"
                  className="text-gray-600 hover:text-emerald-500 p-0 h-auto"
                  asChild
                >
                  <a
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`Follow us on ${social.name}`}
                  >
                    {social.name}
                  </a>
                </Button>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-base sm:text-lg font-bold tracking-wide mb-4 sm:mb-6 uppercase">
              Quick Links
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-1 gap-2 sm:gap-3">
              {quickLinks.map((link) => (
                <Button
                  key={link.name}
                  variant="link"
                  className="justify-start text-gray-600 hover:text-emerald-500 p-0 h-auto"
                  asChild
                >
                  <a href={link.href}>{link.name}</a>
                </Button>
              ))}
            </div>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-base sm:text-lg font-bold tracking-wide mb-4 sm:mb-6 uppercase">
              Stay Updated
            </h3>
            <p className="text-gray-600 mb-4">
              Get the latest insights and project updates.
            </p>
            <form onSubmit={handleSubmit} className="space-y-3">
              <Input
                type="email"
                placeholder="Your email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value)
                  if (error || success) clearMessages()
                }}
                disabled={isSubmitting}
                required
              />
              <Button
                type="submit"
                className="w-full bg-emerald-500 hover:bg-emerald-600"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
              </Button>
              {error && (
                <p className="text-sm text-red-600" role="alert">
                  {error}
                </p>
              )}
              {success && (
                <p className="text-sm text-emerald-600" role="alert">
                  Successfully subscribed!
                </p>
              )}
            </form>
          </div>
        </div>

        <div className="border-t border-emerald-200 my-8 sm:my-12"></div>

        {/* Bottom Section */}
        <div className="flex flex-col sm:flex-row justify-between items-center text-center sm:text-left">
          <p className="text-gray-600 text-sm">
            &copy; {new Date().getFullYear()} {APP_CONFIG.name}. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-6 mt-4 sm:mt-0">
            {legalLinks.map((link) => (
              <Button
                key={link.name}
                variant="link"
                className="text-gray-600 hover:text-emerald-500 p-0 h-auto text-sm"
                asChild
              >
                <a href={link.href}>{link.name}</a>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </footer>
  )
}
