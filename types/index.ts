// Domain Types
export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  icon: string
  category: ServiceCategory
}

export interface PortfolioItem {
  id: string
  title: string
  client: string
  category: PortfolioCategory
  description: string
  image: string
  tags: string[]
  featured: boolean
  url?: string
  completedAt: Date
}

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: Author
  publishedAt: Date
  readTime: string
  category: BlogCategory
  tags: string[]
  image: string
  featured: boolean
  slug: string
}

export interface Testimonial {
  id: string
  quote: string
  author: Author
  company: string
  rating: number
  image?: string
  featured: boolean
}

export interface Author {
  id: string
  name: string
  email: string
  avatar?: string
  bio?: string
  socialLinks?: SocialLinks
}

export interface ContactForm {
  firstName: string
  lastName: string
  email: string
  company?: string
  service?: ServiceCategory
  budget?: BudgetRange
  message: string
  privacyConsent: boolean
}

export interface SocialLinks {
  twitter?: string
  linkedin?: string
  github?: string
  dribbble?: string
  instagram?: string
}

// Enums
export enum ServiceCategory {
  WEB_DEVELOPMENT = 'web-development',
  BRAND_IDENTITY = 'brand-identity',
  UI_UX_DESIGN = 'ui-ux-design',
  CONSULTATION = 'consultation',
}

export enum PortfolioCategory {
  WEB_DEVELOPMENT = 'web-development',
  BRANDING = 'branding',
  UI_UX_DESIGN = 'ui-ux-design',
  E_COMMERCE = 'e-commerce',
}

export enum BlogCategory {
  DESIGN = 'design',
  DEVELOPMENT = 'development',
  BRANDING = 'branding',
  MARKETING = 'marketing',
  E_COMMERCE = 'e-commerce',
}

export enum BudgetRange {
  SMALL = '5k-10k',
  MEDIUM = '10k-25k',
  LARGE = '25k-50k',
  ENTERPRISE = '50k+',
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface NavigationItem {
  name: string
  href: string
  id: string
  external?: boolean
}

// Performance Monitoring Types
export interface PerformanceMetrics {
  lcp?: number
  fid?: number
  cls?: number
  fcp?: number
  ttfb?: number
}

// Error Types
export interface AppError {
  message: string
  code?: string
  statusCode?: number
  stack?: string
}
