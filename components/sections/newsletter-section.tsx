'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Mail, CheckCircle, AlertCircle } from 'lucide-react'
import { useNewsletterSubscription } from '@/hooks/use-contact-form'
import { cn } from '@/lib/utils'

interface NewsletterSectionProps {
  className?: string
  variant?: 'default' | 'compact'
}

export function NewsletterSection({ className, variant = 'default' }: NewsletterSectionProps) {
  const {
    email,
    setEmail,
    subscribe,
    isSubmitting,
    error,
    success,
    clearMessages,
  } = useNewsletterSubscription()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    subscribe()
  }

  if (variant === 'compact') {
    return (
      <div className={cn('bg-emerald-50 p-6 rounded-lg', className)}>
        <div className="text-center mb-4">
          <Mail className="w-8 h-8 text-emerald-600 mx-auto mb-2" />
          <h3 className="text-lg font-bold text-gray-900 mb-2">Stay Updated</h3>
          <p className="text-sm text-gray-600">
            Get the latest insights and project updates delivered to your inbox.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-3">
          <Input
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value)
              if (error || success) clearMessages()
            }}
            disabled={isSubmitting}
            required
          />
          <Button
            type="submit"
            className="w-full bg-emerald-500 hover:bg-emerald-600"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
          </Button>
          
          {error && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertCircle className="w-4 h-4" />
              {error}
            </div>
          )}
          
          {success && (
            <div className="flex items-center gap-2 text-sm text-emerald-600">
              <CheckCircle className="w-4 h-4" />
              Successfully subscribed!
            </div>
          )}
        </form>
      </div>
    )
  }

  return (
    <section className={cn('py-16 sm:py-24 bg-emerald-600', className)}>
      <div className="container mx-auto px-4 sm:px-6">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-white/10 border-white/20 backdrop-blur-sm text-white">
            <CardContent className="p-8 sm:p-12">
              <div className="text-center mb-8">
                <Badge variant="secondary" className="mb-4 bg-white/20 text-white border-white/30 hover:bg-white/20">
                  Newsletter
                </Badge>
                <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold tracking-tight mb-4">
                  STAY IN THE <span className="text-emerald-200">LOOP</span>
                </h2>
                <p className="text-lg sm:text-xl text-emerald-100 leading-relaxed max-w-2xl mx-auto">
                  Get exclusive insights, design tips, and industry updates delivered straight to your inbox. Join our
                  community of digital innovators.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="max-w-md mx-auto">
                <div className="flex flex-col sm:flex-row gap-4">
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value)
                      if (error || success) clearMessages()
                    }}
                    disabled={isSubmitting}
                    required
                    className="flex-1 bg-white/10 border-white/30 text-white placeholder:text-white/70 focus:border-white/50"
                  />
                  <Button
                    type="submit"
                    className="bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
                  </Button>
                </div>

                {error && (
                  <div className="flex items-center gap-2 mt-4 text-red-200">
                    <AlertCircle className="w-5 h-5" />
                    <span>{error}</span>
                  </div>
                )}

                {success && (
                  <div className="flex items-center gap-2 mt-4 text-emerald-200">
                    <CheckCircle className="w-5 h-5" />
                    <span>Successfully subscribed! Check your email for confirmation.</span>
                  </div>
                )}

                <p className="text-sm text-emerald-200 mt-4 text-center">
                  We respect your privacy. Unsubscribe at any time.
                </p>
              </form>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-12 pt-8 border-t border-white/20">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-200 mb-1">Weekly</div>
                  <div className="text-sm text-white/80">Fresh insights</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-200 mb-1">5,000+</div>
                  <div className="text-sm text-white/80">Subscribers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-200 mb-1">No Spam</div>
                  <div className="text-sm text-white/80">Quality content only</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
