import { PortfolioItem, PortfolioCategory } from '@/types'

export const portfolioItems: PortfolioItem[] = [
  {
    id: 'ecommerce-platform',
    title: 'E-Commerce Platform',
    client: 'RetailCorp',
    category: PortfolioCategory.WEB_DEVELOPMENT,
    description: 'A modern, scalable e-commerce platform built with Next.js and Stripe integration, featuring advanced product filtering, wishlist functionality, and seamless checkout experience.',
    image: 'https://picsum.photos/800/600?random=1',
    tags: ['Next.js', 'TypeScript', 'Stripe', 'Tailwind CSS', 'PostgreSQL'],
    featured: true,
    url: 'https://retailcorp-demo.com',
    completedAt: new Date('2024-01-15'),
  },
  {
    id: 'brand-identity-startup',
    title: 'Brand Identity Design',
    client: 'StartupXYZ',
    category: PortfolioCategory.BRANDING,
    description: 'Complete brand identity package including logo design, color palette, typography system, and brand guidelines for a innovative tech startup.',
    image: 'https://picsum.photos/800/600?random=2',
    tags: ['Brand Design', 'Logo', 'Visual Identity', 'Guidelines'],
    featured: true,
    completedAt: new Date('2024-01-12'),
  },
  {
    id: 'mobile-app-design',
    title: 'Mobile App Design',
    client: 'TechFlow',
    category: PortfolioCategory.UI_UX_DESIGN,
    description: 'User-centered mobile app design with intuitive navigation, modern interface, and seamless user experience across iOS and Android platforms.',
    image: 'https://picsum.photos/800/600?random=3',
    tags: ['Mobile Design', 'UI/UX', 'Figma', 'Prototyping'],
    featured: true,
    completedAt: new Date('2024-01-10'),
  },
  {
    id: 'corporate-website',
    title: 'Corporate Website',
    client: 'BusinessPro',
    category: PortfolioCategory.WEB_DEVELOPMENT,
    description: 'Professional corporate website with content management system, multi-language support, and advanced SEO optimization.',
    image: 'https://picsum.photos/800/600?random=4',
    tags: ['React', 'CMS', 'SEO', 'Multi-language'],
    featured: false,
    completedAt: new Date('2024-01-08'),
  },
  {
    id: 'creative-campaign',
    title: 'Creative Campaign',
    client: 'ArtisticCo',
    category: PortfolioCategory.BRANDING,
    description: 'Comprehensive creative campaign including visual identity, marketing materials, and digital assets for product launch.',
    image: 'https://picsum.photos/800/600?random=5',
    tags: ['Campaign Design', 'Marketing', 'Print Design', 'Digital Assets'],
    featured: false,
    completedAt: new Date('2024-01-05'),
  },
  {
    id: 'dashboard-ui',
    title: 'Analytics Dashboard',
    client: 'DataViz',
    category: PortfolioCategory.UI_UX_DESIGN,
    description: 'Complex data visualization dashboard with interactive charts, real-time updates, and customizable widgets for business intelligence.',
    image: 'https://picsum.photos/800/600?random=6',
    tags: ['Dashboard', 'Data Visualization', 'React', 'D3.js'],
    featured: false,
    completedAt: new Date('2024-01-03'),
  },
  {
    id: 'saas-platform',
    title: 'SaaS Platform',
    client: 'CloudTech',
    category: PortfolioCategory.WEB_DEVELOPMENT,
    description: 'Full-stack SaaS platform with user authentication, subscription management, and comprehensive admin dashboard.',
    image: 'https://picsum.photos/800/600?random=7',
    tags: ['SaaS', 'Full-stack', 'Authentication', 'Subscriptions'],
    featured: false,
    completedAt: new Date('2023-12-28'),
  },
  {
    id: 'restaurant-branding',
    title: 'Restaurant Branding',
    client: 'Gourmet Bistro',
    category: PortfolioCategory.BRANDING,
    description: 'Complete restaurant branding package including logo, menu design, signage, and digital presence.',
    image: 'https://picsum.photos/800/600?random=8',
    tags: ['Restaurant', 'Menu Design', 'Signage', 'Print Design'],
    featured: false,
    completedAt: new Date('2023-12-25'),
  },
  {
    id: 'fitness-app',
    title: 'Fitness App Interface',
    client: 'FitLife',
    category: PortfolioCategory.UI_UX_DESIGN,
    description: 'Modern fitness app interface with workout tracking, progress visualization, and social features.',
    image: 'https://picsum.photos/800/600?random=9',
    tags: ['Fitness App', 'Mobile UI', 'Tracking', 'Social Features'],
    featured: false,
    completedAt: new Date('2023-12-20'),
  },
]

export function getPortfolioItemById(id: string): PortfolioItem | undefined {
  return portfolioItems.find(item => item.id === id)
}

export function getPortfolioItemsByCategory(category: PortfolioCategory): PortfolioItem[] {
  return portfolioItems.filter(item => item.category === category)
}

export function getFeaturedPortfolioItems(): PortfolioItem[] {
  return portfolioItems.filter(item => item.featured)
}

export function getRecentPortfolioItems(limit: number = 6): PortfolioItem[] {
  return portfolioItems
    .sort((a, b) => b.completedAt.getTime() - a.completedAt.getTime())
    .slice(0, limit)
}

export function searchPortfolioItems(query: string): PortfolioItem[] {
  const lowercaseQuery = query.toLowerCase()
  return portfolioItems.filter(
    item =>
      item.title.toLowerCase().includes(lowercaseQuery) ||
      item.client.toLowerCase().includes(lowercaseQuery) ||
      item.description.toLowerCase().includes(lowercaseQuery) ||
      item.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  )
}
