import { useState, useEffect, useCallback } from 'react'
import { PerformanceMetrics } from '@/types'

// Hook for monitoring Core Web Vitals
export function useWebVitals() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({})

  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    // Largest Contentful Paint (LCP)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number }
      
      setMetrics(prev => ({
        ...prev,
        lcp: lastEntry.startTime,
      }))
    })

    // First Input Delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        setMetrics(prev => ({
          ...prev,
          fid: entry.processingStart - entry.startTime,
        }))
      })
    })

    // Cumulative Layout Shift (CLS)
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          setMetrics(prev => ({
            ...prev,
            cls: (prev.cls || 0) + entry.value,
          }))
        }
      })
    })

    // First Contentful Paint (FCP)
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        setMetrics(prev => ({
          ...prev,
          fcp: entry.startTime,
        }))
      })
    })

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      fidObserver.observe({ entryTypes: ['first-input'] })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
      fcpObserver.observe({ entryTypes: ['paint'] })
    } catch (error) {
      console.warn('Performance Observer not supported:', error)
    }

    // Time to First Byte (TTFB)
    if ('navigation' in performance) {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        setMetrics(prev => ({
          ...prev,
          ttfb: navigationEntry.responseStart - navigationEntry.requestStart,
        }))
      }
    }

    return () => {
      lcpObserver.disconnect()
      fidObserver.disconnect()
      clsObserver.disconnect()
      fcpObserver.disconnect()
    }
  }, [])

  return metrics
}

// Hook for monitoring page load performance
export function usePageLoadMetrics() {
  const [metrics, setMetrics] = useState<{
    loadTime: number | null
    domContentLoaded: number | null
    firstPaint: number | null
    firstContentfulPaint: number | null
  }>({
    loadTime: null,
    domContentLoaded: null,
    firstPaint: null,
    firstContentfulPaint: null,
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const measureMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paintEntries = performance.getEntriesByType('paint')

      if (navigation) {
        setMetrics({
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || null,
          firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || null,
        })
      }
    }

    if (document.readyState === 'complete') {
      measureMetrics()
    } else {
      window.addEventListener('load', measureMetrics)
    }

    return () => window.removeEventListener('load', measureMetrics)
  }, [])

  return metrics
}

// Hook for monitoring memory usage
export function useMemoryUsage() {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number | null
    totalJSHeapSize: number | null
    jsHeapSizeLimit: number | null
  }>({
    usedJSHeapSize: null,
    totalJSHeapSize: null,
    jsHeapSizeLimit: null,
  })

  useEffect(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return
    }

    const updateMemoryInfo = () => {
      const memory = (performance as any).memory
      if (memory) {
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        })
      }
    }

    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return memoryInfo
}

// Hook for monitoring network information
export function useNetworkInfo() {
  const [networkInfo, setNetworkInfo] = useState<{
    effectiveType: string | null
    downlink: number | null
    rtt: number | null
    saveData: boolean | null
  }>({
    effectiveType: null,
    downlink: null,
    rtt: null,
    saveData: null,
  })

  useEffect(() => {
    if (typeof window === 'undefined' || !('connection' in navigator)) {
      return
    }

    const connection = (navigator as any).connection
    if (connection) {
      const updateNetworkInfo = () => {
        setNetworkInfo({
          effectiveType: connection.effectiveType || null,
          downlink: connection.downlink || null,
          rtt: connection.rtt || null,
          saveData: connection.saveData || null,
        })
      }

      updateNetworkInfo()
      connection.addEventListener('change', updateNetworkInfo)

      return () => connection.removeEventListener('change', updateNetworkInfo)
    }
  }, [])

  return networkInfo
}

// Hook for performance monitoring with reporting
export function usePerformanceMonitor(options?: {
  reportInterval?: number
  onReport?: (metrics: PerformanceMetrics) => void
}) {
  const webVitals = useWebVitals()
  const pageLoad = usePageLoadMetrics()
  const memory = useMemoryUsage()
  const network = useNetworkInfo()

  const reportMetrics = useCallback(() => {
    const allMetrics: PerformanceMetrics = {
      ...webVitals,
      fcp: pageLoad.firstContentfulPaint || undefined,
      ttfb: webVitals.ttfb,
    }

    if (options?.onReport) {
      options.onReport(allMetrics)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics')
      console.log('Web Vitals:', webVitals)
      console.log('Page Load:', pageLoad)
      console.log('Memory:', memory)
      console.log('Network:', network)
      console.groupEnd()
    }
  }, [webVitals, pageLoad, memory, network, options])

  useEffect(() => {
    if (options?.reportInterval) {
      const interval = setInterval(reportMetrics, options.reportInterval)
      return () => clearInterval(interval)
    }
  }, [reportMetrics, options?.reportInterval])

  return {
    webVitals,
    pageLoad,
    memory,
    network,
    reportMetrics,
  }
}

// Hook for detecting slow performance
export function usePerformanceAlert(thresholds?: {
  lcp?: number
  fid?: number
  cls?: number
}) {
  const [alerts, setAlerts] = useState<string[]>([])
  const webVitals = useWebVitals()

  const defaultThresholds = {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1
    ...thresholds,
  }

  useEffect(() => {
    const newAlerts: string[] = []

    if (webVitals.lcp && webVitals.lcp > defaultThresholds.lcp) {
      newAlerts.push(`LCP is slow: ${Math.round(webVitals.lcp)}ms`)
    }

    if (webVitals.fid && webVitals.fid > defaultThresholds.fid) {
      newAlerts.push(`FID is slow: ${Math.round(webVitals.fid)}ms`)
    }

    if (webVitals.cls && webVitals.cls > defaultThresholds.cls) {
      newAlerts.push(`CLS is high: ${webVitals.cls.toFixed(3)}`)
    }

    setAlerts(newAlerts)
  }, [webVitals, defaultThresholds])

  return alerts
}
