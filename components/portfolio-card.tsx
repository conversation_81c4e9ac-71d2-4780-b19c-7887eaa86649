"use client"

import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import { CardContainer, CardBody, CardItem } from "@/components/ui/3d-card"

export interface PortfolioCardProps {
  title: string
  client: string
  category: string
  image: string
  className?: string
  onViewProject?: () => void
}

export function PortfolioCard({
  title,
  client,
  category,
  image,
  className,
  onViewProject
}: PortfolioCardProps) {
  const handleViewProject = () => {
    if (onViewProject) {
      onViewProject()
    } else {
      // Default behavior - could open in new tab or navigate
      console.log(`Viewing project: ${title}`)
    }
  }

  return (
    <CardContainer className={`w-full ${className || ""}`}>
      <CardBody className="relative group h-auto w-full bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300">
        {/* Image */}
        <CardItem translateZ="100" className="w-full aspect-[4/3] overflow-hidden">
          <Image
            src={image || "/placeholder.svg"}
            alt={`${title} - ${client} project`}
            width={400}
            height={300}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
            loading="lazy"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </CardItem>

        {/* Category Badge */}
        <CardItem translateZ="120" className="absolute top-4 left-4">
          <Badge
            variant="secondary"
            className="bg-emerald-500/90 text-white border-0 shadow-lg backdrop-blur-sm px-3 py-1 text-xs font-medium"
          >
            {category}
          </Badge>
        </CardItem>

        {/* Content */}
        <CardItem translateZ="80" className="p-5">
          <h3 className="text-xl font-bold mb-1 text-gray-900 line-clamp-2">{title}</h3>
          <p className="text-gray-600 font-medium">{client}</p>
        </CardItem>

        {/* Action Button */}
        <CardItem translateZ="140" className="absolute top-4 right-4">
          <Button
            size="sm"
            onClick={handleViewProject}
            className="bg-white/90 text-emerald-600 hover:bg-white hover:text-emerald-700 shadow-lg backdrop-blur-sm border border-emerald-200/50 rounded-full w-10 h-10 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <ExternalLink className="w-4 h-4" />
            <span className="sr-only">View {title} project</span>
          </Button>
        </CardItem>

        {/* Accent Line */}
        <CardItem
          translateZ="60"
          className="absolute bottom-0 left-5 right-5 h-1 bg-gradient-to-r from-emerald-500 to-emerald-600 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
        />
      </CardBody>
    </CardContainer>
  )
}
