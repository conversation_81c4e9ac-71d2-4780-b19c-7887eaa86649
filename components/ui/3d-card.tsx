"use client"

import { cn } from "@/lib/utils"
import type React from "react"
import { createContext, useState, useContext, useRef, useEffect, useCallback } from "react"

interface MouseEnterContextType {
  isMouseEntered: boolean
  setIsMouseEntered: React.Dispatch<React.SetStateAction<boolean>>
}

const MouseEnterContext = createContext<MouseEnterContextType | undefined>(undefined)

export interface CardContainerProps {
  children?: React.ReactNode
  className?: string
  containerClassName?: string
}

export const CardContainer = ({
  children,
  className,
  containerClassName,
  ...props
}: CardContainerProps & React.HTMLAttributes<HTMLDivElement>) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isMouseEntered, setIsMouseEntered] = useState(false)

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return
    const { left, top, width, height } = containerRef.current.getBoundingClientRect()
    const x = (e.clientX - left - width / 2) / 25
    const y = (e.clientY - top - height / 2) / 25
    containerRef.current.style.transform = `rotateY(${x}deg) rotateX(${-y}deg)`
  }, [])

  const handleMouseEnter = useCallback(() => {
    setIsMouseEntered(true)
    if (!containerRef.current) return
    containerRef.current.style.transition = "transform 0.1s ease-out"
  }, [])

  const handleMouseLeave = useCallback(() => {
    if (!containerRef.current) return
    setIsMouseEntered(false)
    containerRef.current.style.transition = "transform 0.4s ease-out"
    containerRef.current.style.transform = `rotateY(0deg) rotateX(0deg)`
  }, [])

  return (
    <MouseEnterContext.Provider value={{ isMouseEntered, setIsMouseEntered }}>
      <div
        className={cn("py-2 flex items-center justify-center", containerClassName)}
        style={{
          perspective: "1000px",
        }}
      >
        <div
          ref={containerRef}
          onMouseEnter={handleMouseEnter}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          className={cn("flex items-center justify-center relative transition-all duration-200 ease-linear", className)}
          style={{
            transformStyle: "preserve-3d",
          }}
          {...props}
        >
          {children}
        </div>
      </div>
    </MouseEnterContext.Provider>
  )
}

export interface CardBodyProps {
  children: React.ReactNode
  className?: string
}

export const CardBody = ({
  children,
  className,
  ...props
}: CardBodyProps & React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn("h-96 w-96 [transform-style:preserve-3d] [&>*]:[transform-style:preserve-3d]", className)}
      {...props}
    >
      {children}
    </div>
  )
}

export interface CardItemProps {
  as?: React.ElementType
  children: React.ReactNode
  className?: string
  translateX?: number | string
  translateY?: number | string
  translateZ?: number | string
  rotateX?: number | string
  rotateY?: number | string
  rotateZ?: number | string
}

export const CardItem = ({
  as: Tag = "div",
  children,
  className,
  translateX = 0,
  translateY = 0,
  translateZ = 0,
  rotateX = 0,
  rotateY = 0,
  rotateZ = 0,
  ...props
}: CardItemProps & React.HTMLAttributes<HTMLElement>) => {
  const ref = useRef<HTMLElement>(null)
  const { isMouseEntered } = useMouseEnter()

  useEffect(() => {
    if (!ref.current) return
    if (isMouseEntered) {
      ref.current.style.transform = `translateX(${translateX}px) translateY(${translateY}px) translateZ(${translateZ}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg)`
    } else {
      ref.current.style.transform = `translateX(0px) translateY(0px) translateZ(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)`
    }
  }, [isMouseEntered, translateX, translateY, translateZ, rotateX, rotateY, rotateZ])

  return (
    <Tag ref={ref} className={cn("w-fit transition duration-200 ease-linear", className)} {...props}>
      {children}
    </Tag>
  )
}

// Create a hook to use the context
export const useMouseEnter = () => {
  const context = useContext(MouseEnterContext)
  if (context === undefined) {
    throw new Error("useMouseEnter must be used within a CardContainer")
  }
  return context
}
