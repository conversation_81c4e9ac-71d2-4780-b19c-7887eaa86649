"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back, useMemo } from "react"
import Image from "next/image"
import {
  ChevronLeft,
  ChevronRight,
  Menu,
  Palette,
  Code,
  Smartphone,
  Star,
  Mail,
  Phone,
  MapPin,
  Clock,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Checkbox } from "@/components/ui/checkbox"
import ParticleBackground from "@/components/particle-background"
import { PortfolioCard } from "@/components/portfolio-card"
import BlogPage from "@/components/blog-page"
import { FlipWords } from "@/components/ui/flip-words"

interface Testimonial {
  quote: string
  name: string
  company: string
  image: string
  rating: number
}

interface PortfolioItem {
  title: string
  client: string
  category: string
  image: string
}

interface Service {
  icon: typeof Code
  title: string
  description: string
  features: string[]
}

export function FlipWordsElement() {
  const words = ["creativity.", "strategy.", "innovation."];
 
  return (
    <div className="flex justify-center items-center">
      <div className="text-lg sm:text-xl md:text-2xl font-light leading-relaxed mb-8 sm:mb-12 max-w-3xl mx-auto text-gray-600 px-4 sm:px-0">
        Empowering your digital presence through
        <FlipWords words={words} /> <br />
      </div>
    </div>
  );
}

export default function HomePage() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [currentPage, setCurrentPage] = useState("home")

  const scrollToSection = useCallback((sectionId: string) => {
    if (sectionId === "blog") {
      setCurrentPage("blog")
      // Scroll to top when switching to blog page
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      })
      return
    }

    setCurrentPage("home")
    setTimeout(() => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
        })
      }
    }, 100)
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const testimonials: Testimonial[] = useMemo(() => [
    {
      quote:
        "DSF Solutions transformed our digital presence completely. Their attention to detail and creative vision exceeded our expectations.",
      name: "Sarah Johnson",
      company: "TechStart Inc.",
      image: "/placeholder.svg?height=60&width=60",
      rating: 5,
    },
    {
      quote:
        "Working with DSF was a game-changer. They delivered a stunning website that perfectly captures our brand essence.",
      name: "Michael Chen",
      company: "Creative Studios",
      image: "/placeholder.svg?height=60&width=60",
      rating: 5,
    },
    {
      quote:
        "Professional, innovative, and results-driven. DSF Solutions is our go-to partner for all digital projects.",
      name: "Emily Rodriguez",
      company: "Global Ventures",
      image: "/placeholder.svg?height=60&width=60",
      rating: 5,
    },
  ], [])

  const portfolioItems: PortfolioItem[] = useMemo(() => [
    {
      title: "E-Commerce Platform",
      client: "RetailCorp",
      category: "Web Development",
      image: "https://picsum.photos/300/400?random=1",
    },
    {
      title: "Brand Identity",
      client: "StartupXYZ",
      category: "Branding",
      image: "https://picsum.photos/300/400?random=2",
    },
    {
      title: "Mobile App Design",
      client: "TechFlow",
      category: "UI/UX Design",
      image: "https://picsum.photos/300/400?random=3",
    },
    {
      title: "Corporate Website",
      client: "BusinessPro",
      category: "Web Development",
      image: "https://picsum.photos/300/400?random=4",
    },
    {
      title: "Creative Campaign",
      client: "ArtisticCo",
      category: "Branding",
      image: "https://picsum.photos/300/400?random=5",
    },
    {
      title: "Dashboard UI",
      client: "DataViz",
      category: "UI/UX Design",
      image: "https://picsum.photos/300/400?random=6",
    },
  ], [])

  const services: Service[] = useMemo(() => [
    {
      icon: Code,
      title: "Web Development",
      description:
        "Creating stunning, responsive websites that captivate audiences and drive conversions through thoughtful design and seamless user experience.",
      features: ["Responsive Design", "Performance Optimization", "SEO Ready", "Modern Frameworks"],
    },
    {
      icon: Palette,
      title: "Brand Identity",
      description:
        "Developing powerful brand identities that resonate with your target audience and establish lasting connections in the marketplace.",
      features: ["Logo Design", "Brand Guidelines", "Visual Identity", "Brand Strategy"],
    },
    {
      icon: Smartphone,
      title: "UI/UX Design",
      description:
        "Designing intuitive interfaces and user experiences that prioritize usability while maintaining aesthetic excellence and brand consistency.",
      features: ["User Research", "Wireframing", "Prototyping", "Usability Testing"],
    },
  ], [])

  const nextTestimonial = useCallback(() => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }, [testimonials.length])

  const prevTestimonial = useCallback(() => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }, [testimonials.length])

  const navigationItems = useMemo(() => [
    { name: "HOME", id: "home" },
    { name: "SERVICES", id: "services" },
    { name: "PORTFOLIO", id: "portfolio" },
    { name: "BLOG", id: "blog" },
    { name: "ABOUT", id: "testimonials" },
    { name: "CONTACT", id: "contact" },
  ], [])

  // const flipWords = useMemo(() => ["creativity.", "strategy.", "innovation."], [])

  if (currentPage === "blog") {
    return (
      <div className="min-h-screen bg-white text-gray-900">
        {/* Header */}
        <header
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-200 ${
            isScrolled ? "bg-white/70 backdrop-blur-md shadow-sm" : "bg-transparent"
          }`}
        >
          <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
            <div className="flex items-center justify-between">
              {/* Logo Section */}
              <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
                <Image
                  src="/dsf-logo.png"
                  alt="DSF Solutions Logo"
                  width={32}
                  height={32}
                  className="w-7 h-7 sm:w-8 sm:h-8"
                />
                <div className="text-lg sm:text-xl lg:text-2xl font-bold tracking-tight">
                  DSF <span className="text-emerald-500">SOLUTIONS</span>
                </div>
              </div>

              {/* Desktop Navigation - More responsive breakpoints */}
              <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6 2xl:space-x-8">
                {navigationItems.map((item) => (
                  <Button
                    key={item.name}
                    variant="ghost"
                    onClick={() => scrollToSection(item.id)}
                    className={`text-xs xl:text-sm font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2 xl:px-3 ${
                      item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                    }`}
                  >
                    {item.name}
                  </Button>
                ))}
              </nav>

              {/* Tablet Navigation - Show for medium screens */}
              <nav className="hidden md:flex lg:hidden items-center space-x-2">
                {navigationItems.slice(0, 4).map((item) => (
                  <Button
                    key={item.name}
                    variant="ghost"
                    onClick={() => scrollToSection(item.id)}
                    className={`text-xs font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2 ${
                      item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                    }`}
                  >
                    {item.name}
                  </Button>
                ))}
                {/* More button for remaining items */}
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="sm" className="text-xs font-medium tracking-wider px-2">
                      MORE
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-[280px]">
                    <nav className="flex flex-col space-y-4 mt-8">
                      {navigationItems.slice(4).map((item) => (
                        <Button
                          key={item.name}
                          variant="ghost"
                          onClick={() => scrollToSection(item.id)}
                          className={`justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer ${
                            item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                          }`}
                        >
                          {item.name}
                        </Button>
                      ))}
                    </nav>
                  </SheetContent>
                </Sheet>
              </nav>

              {/* Mobile Menu Button */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="md:hidden">
                    <Menu size={24} />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[280px] sm:w-[320px]">
                  <nav className="flex flex-col space-y-4 mt-8">
                    {navigationItems.map((item) => (
                      <Button
                        key={item.name}
                        variant="ghost"
                        onClick={() => scrollToSection(item.id)}
                        className={`justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer ${
                          item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                        }`}
                      >
                        {item.name}
                      </Button>
                    ))}
                  </nav>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </header>

        <div className="pt-20">
          <BlogPage />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white text-gray-900">
      {/* Header */}
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-200 ${
          isScrolled ? "bg-white/70 backdrop-blur-md shadow-sm" : "bg-transparent"
        }`}
      >
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
              <Image
                src="/dsf-logo.png"
                alt="DSF Solutions Logo"
                width={32}
                height={32}
                className="w-7 h-7 sm:w-8 sm:h-8"
              />
              <div className="text-lg sm:text-xl lg:text-2xl font-bold tracking-tight">
                DSF <span className="text-emerald-500">SOLUTIONS</span>
              </div>
            </div>

            {/* Desktop Navigation - More responsive breakpoints */}
            <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6 2xl:space-x-8">
              {navigationItems.map((item) => (
                <Button
                  key={item.name}
                  variant="ghost"
                  onClick={() => scrollToSection(item.id)}
                  className={`text-xs xl:text-sm font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2 xl:px-3 ${
                    item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                  }`}
                >
                  {item.name}
                </Button>
              ))}
            </nav>

            {/* Tablet Navigation - Show for medium screens */}
            <nav className="hidden md:flex lg:hidden items-center space-x-2">
              {navigationItems.slice(0, 4).map((item) => (
                <Button
                  key={item.name}
                  variant="ghost"
                  onClick={() => scrollToSection(item.id)}
                  className={`text-xs font-medium tracking-wider hover:text-emerald-500 transition-colors duration-200 cursor-pointer px-2 ${
                    item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                  }`}
                >
                  {item.name}
                </Button>
              ))}
              {/* More button for remaining items */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-xs font-medium tracking-wider px-2">
                    MORE
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[280px]">
                  <nav className="flex flex-col space-y-4 mt-8">
                    {navigationItems.slice(4).map((item) => (
                      <Button
                        key={item.name}
                        variant="ghost"
                        onClick={() => scrollToSection(item.id)}
                        className={`justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer ${
                          item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                        }`}
                      >
                        {item.name}
                      </Button>
                    ))}
                  </nav>
                </SheetContent>
              </Sheet>
            </nav>

            {/* Mobile Menu Button */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu size={24} />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px] sm:w-[320px]">
                <nav className="flex flex-col space-y-4 mt-8">
                  {navigationItems.map((item) => (
                    <Button
                      key={item.name}
                      variant="ghost"
                      onClick={() => scrollToSection(item.id)}
                      className={`justify-start text-sm font-medium tracking-widest hover:text-emerald-500 transition-colors duration-200 cursor-pointer ${
                        item.id === "blog" && currentPage === "blog" ? "text-emerald-500" : ""
                      }`}
                    >
                      {item.name}
                    </Button>
                  ))}
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section
        id="home"
        className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-emerald-50/50 to-white pt-24 sm:pt-20 md:pt-16"
        aria-label="Hero section"
      >
        <ParticleBackground />

        <div className="absolute inset-0 opacity-10" style={{ zIndex: 2 }} aria-hidden="true">
          <div className="absolute top-1/4 left-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-emerald-500 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-emerald-600 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-6 sm:px-6 text-center relative z-10">
          <Badge variant="secondary" className="mb-6 sm:mb-8 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
            Digital Excellence Redefined
          </Badge>

          <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black tracking-tight leading-[0.9] sm:leading-none mb-6 sm:mb-8 px-2 sm:px-0">
            CRAFTING
            <br />
            <span className="text-emerald-500">DIGITAL</span>
            <br />
            <span className="block sm:inline sm:ml-3">EXPERIENCES</span>
          </h1>

          <FlipWordsElement />

          <div className="flex flex-col sm:flex-row gap-4 justify-center px-4 sm:px-0">
            <Button
              size="lg"
              onClick={() => scrollToSection("contact")}
              className="bg-emerald-500 hover:bg-emerald-600 px-8 py-6 text-base sm:text-lg font-semibold tracking-wide"
            >
              START YOUR PROJECT
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => scrollToSection("portfolio")}
              className="border-2 border-emerald-200 text-emerald-700 hover:bg-emerald-50 px-8 py-6 text-base sm:text-lg font-semibold tracking-wide"
            >
              VIEW OUR WORK
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16 sm:py-24 bg-white" aria-labelledby="services-heading">
        <div className="container mx-auto px-4 sm:px-6">
          <header className="text-center mb-12 sm:mb-20">
            <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
              Our Expertise
            </Badge>
            <h2 id="services-heading" className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
              WHAT WE <span className="text-emerald-500">CREATE</span>
            </h2>
            <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              We craft digital experiences that drive results and inspire action through innovative design and
              development.
            </p>
          </header>

          <div className="grid gap-8 md:grid-cols-3" role="list">
            {services.map((service, index) => (
              <Card key={`service-${index}`} className="group hover:border-emerald-200 hover:shadow-xl transition-all duration-300" role="listitem">
                <CardHeader>
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300" aria-hidden="true">
                    <service.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight">{service.title}</CardTitle>
                  <CardDescription className="text-gray-600 leading-relaxed">{service.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2" aria-label={`${service.title} features`}>
                    {service.features.map((feature, featureIndex) => (
                      <li key={`${service.title}-feature-${featureIndex}`} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full" aria-hidden="true"></div>
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-16 sm:py-24 bg-emerald-50/30">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
              Featured Work
            </Badge>
            <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
              OUR <span className="text-emerald-500">PORTFOLIO</span>
            </h2>
            <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              A showcase of our latest projects and creative solutions that drive business growth.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {portfolioItems.map((item, index) => (
              <div key={`${item.title}-${index}`} className="h-full">
                <PortfolioCard
                  title={item.title}
                  client={item.client}
                  category={item.category}
                  image={item.image}
                  onViewProject={() => {
                    console.log(`Viewing project: ${item.title} for ${item.client}`)
                    // Add your project viewing logic here
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-16 sm:py-24 bg-emerald-600 text-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center mb-12 sm:mb-20">
            <Badge variant="secondary" className="mb-4 bg-white/20 text-white border-white/30 hover:bg-white/20">
              Client Success Stories
            </Badge>
            <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
              TRUSTED BY <span className="text-emerald-200">LEADERS</span>
            </h2>
          </div>

          <div className="max-w-4xl mx-auto">
            <Card className="bg-white/10 border-white/20 backdrop-blur-sm text-white">
              <CardContent className="p-6 sm:p-8">
                <div className="text-center">
                  <div className="flex justify-center mb-4 sm:mb-6">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-200 fill-current" />
                    ))}
                  </div>

                  <blockquote className="text-xl sm:text-2xl md:text-3xl font-light italic leading-relaxed mb-6 sm:mb-8">
                    "{testimonials[currentTestimonial].quote}"
                  </blockquote>

                  <div className="flex items-center justify-center space-x-4">
                    <Image
                      src={testimonials[currentTestimonial].image || "/placeholder.svg"}
                      alt={testimonials[currentTestimonial].name}
                      width={60}
                      height={60}
                      className="w-12 h-12 sm:w-14 sm:h-14 rounded-full border-2 border-white/30"
                    />
                    <div className="text-left">
                      <div className="font-semibold text-base sm:text-lg">{testimonials[currentTestimonial].name}</div>
                      <div className="text-emerald-200 text-sm sm:text-base">
                        {testimonials[currentTestimonial].company}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-8">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={prevTestimonial}
                    className="text-white hover:bg-white/10"
                  >
                    <ChevronLeft size={24} />
                    <span className="sr-only">Previous testimonial</span>
                  </Button>

                  <div className="flex space-x-2">
                    {testimonials.map((_, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        onClick={() => setCurrentTestimonial(index)}
                        className={`w-2.5 h-2.5 p-0 rounded-full transition-colors ${
                          index === currentTestimonial ? "bg-emerald-200" : "bg-white/30"
                        }`}
                      >
                        <span className="sr-only">Go to testimonial {index + 1}</span>
                      </Button>
                    ))}
                  </div>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={nextTestimonial}
                    className="text-white hover:bg-white/10"
                  >
                    <ChevronRight size={24} />
                    <span className="sr-only">Next testimonial</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 sm:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12 sm:mb-16">
              <Badge variant="secondary" className="mb-4 bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
                Get In Touch
              </Badge>
              <h2 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-4 sm:mb-6">
                LET'S <span className="text-emerald-500">COLLABORATE</span>
              </h2>
              <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Ready to transform your digital presence? Let's discuss your project and create something extraordinary
                together.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 sm:gap-16">
              {/* Contact Info */}
              <div className="space-y-6 sm:space-y-8">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight uppercase flex items-center">
                      <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-500 mr-3" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Mail className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-gray-600"><EMAIL></p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Phone className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-gray-600">+****************</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <MapPin className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium">Address</p>
                        <p className="text-gray-600">
                          123 Design Street
                          <br />
                          Creative District, CD 12345
                        </p>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 my-6"></div>

                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Clock className="w-5 h-5 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium">Office Hours</p>
                        <div className="text-gray-600 space-y-1">
                          <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                          <p>Saturday: 10:00 AM - 4:00 PM</p>
                          <p>Sunday: Closed</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Contact Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl sm:text-2xl font-bold tracking-tight">Send us a message</CardTitle>
                  <CardDescription>Fill out the form below and we'll get back to you within 24 hours.</CardDescription>
                </CardHeader>
                <CardContent>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input id="firstName" placeholder="John" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input id="lastName" placeholder="Doe" required />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Input id="email" type="email" placeholder="<EMAIL>" required />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      <Input id="company" placeholder="Your Company" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="service">Service Interested In</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a service" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="web-development">Web Development</SelectItem>
                          <SelectItem value="branding">Brand Identity</SelectItem>
                          <SelectItem value="ux-ui">UI/UX Design</SelectItem>
                          <SelectItem value="consultation">Consultation</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budget">Project Budget</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select budget range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5k-10k">$5,000 - $10,000</SelectItem>
                          <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                          <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                          <SelectItem value="50k+">$50,000+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Project Details *</Label>
                      <Textarea
                        id="message"
                        placeholder="Tell us about your project, goals, and timeline..."
                        className="min-h-[120px]"
                        required
                      />
                    </div>

                    <div className="flex items-start space-x-3">
                      <Checkbox id="privacy" required />
                      <Label htmlFor="privacy" className="text-sm text-gray-600 leading-relaxed">
                        I agree to the privacy policy and terms of service. I consent to DSF Solutions contacting me
                        about this inquiry. *
                      </Label>
                    </div>

                    <Button type="submit" className="w-full bg-emerald-500 hover:bg-emerald-600" size="lg">
                      SEND MESSAGE
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-emerald-50 py-12 sm:py-16 border-t border-emerald-100">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {/* Brand */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4 sm:mb-6">
                <Image
                  src="/dsf-logo.png"
                  alt="DSF Solutions Logo"
                  width={32}
                  height={32}
                  className="w-7 h-7 sm:w-8 sm:h-8"
                />
                <div className="text-xl sm:text-2xl font-bold tracking-tight">
                  DSF <span className="text-emerald-500">SOLUTIONS</span>
                </div>
              </div>
              <p className="text-gray-600 mb-6 max-w-md">
                Empowering businesses through innovative digital solutions, creative design, and strategic thinking.
              </p>
              <div className="flex flex-wrap gap-4">
                {["Instagram", "Twitter", "LinkedIn", "Dribbble"].map((social) => (
                  <Button key={social} variant="link" className="text-gray-600 hover:text-emerald-500 p-0 h-auto">
                    {social}
                  </Button>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-base sm:text-lg font-bold tracking-wide mb-4 sm:mb-6 uppercase">Quick Links</h3>
              <div className="grid grid-cols-2 sm:grid-cols-1 gap-2 sm:gap-3">
                {["About Us", "Services", "Portfolio", "Blog", "Contact", "Careers"].map((link) => (
                  <Button
                    key={link}
                    variant="link"
                    className="justify-start text-gray-600 hover:text-emerald-500 p-0 h-auto"
                  >
                    {link}
                  </Button>
                ))}
              </div>
            </div>

            {/* Newsletter */}
            <div>
              <h3 className="text-base sm:text-lg font-bold tracking-wide mb-4 sm:mb-6 uppercase">Stay Updated</h3>
              <p className="text-gray-600 mb-4">Get the latest insights and project updates.</p>
              <div className="space-y-3">
                <Input type="email" placeholder="Your email" />
                <Button className="w-full bg-emerald-500 hover:bg-emerald-600">SUBSCRIBE</Button>
              </div>
            </div>
          </div>

          <div className="border-t border-emerald-200 my-8 sm:my-12"></div>

          <div className="flex flex-col sm:flex-row justify-between items-center text-center sm:text-left">
            <p className="text-gray-600 text-sm">
              &copy; {new Date().getFullYear()} DSF Solutions. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-4 sm:gap-6 mt-4 sm:mt-0">
              <Button variant="link" className="text-gray-600 hover:text-emerald-500 p-0 h-auto text-sm">
                Privacy Policy
              </Button>
              <Button variant="link" className="text-gray-600 hover:text-emerald-500 p-0 h-auto text-sm">
                Terms of Service
              </Button>
              <Button variant="link" className="text-gray-600 hover:text-emerald-500 p-0 h-auto text-sm">
                Cookie Policy
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
