import { useState, useEffect, useCallback } from 'react'
import { 
  Service, 
  PortfolioItem, 
  BlogPost, 
  Testimonial, 
  PaginatedResponse,
  BlogCategory,
  PortfolioCategory 
} from '@/types'
import { ContentService } from '@/services/content'

// Generic hook for async data fetching
function useAsyncData<T>(
  asyncFunction: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await asyncFunction()
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, dependencies)

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}

// Services hooks
export function useServices() {
  return useAsyncData(() => ContentService.getServices())
}

export function useService(id: string) {
  return useAsyncData(() => ContentService.getServiceById(id), [id])
}

export function useFeaturedServices() {
  return useAsyncData(() => ContentService.getFeaturedServices())
}

// Portfolio hooks
export function usePortfolioItems(params?: {
  category?: PortfolioCategory
  featured?: boolean
  limit?: number
  page?: number
}) {
  return useAsyncData(() => ContentService.getPortfolioItems(params), [
    params?.category,
    params?.featured,
    params?.limit,
    params?.page,
  ])
}

export function usePortfolioItem(id: string) {
  return useAsyncData(() => ContentService.getPortfolioItemById(id), [id])
}

export function useFeaturedPortfolioItems() {
  return useAsyncData(() => ContentService.getFeaturedPortfolioItems())
}

// Blog hooks
export function useBlogPosts(params?: {
  category?: BlogCategory
  featured?: boolean
  limit?: number
  page?: number
  search?: string
}) {
  return useAsyncData(() => ContentService.getBlogPosts(params), [
    params?.category,
    params?.featured,
    params?.limit,
    params?.page,
    params?.search,
  ])
}

export function useBlogPost(id: string) {
  return useAsyncData(() => ContentService.getBlogPostById(id), [id])
}

export function useBlogPostBySlug(slug: string) {
  return useAsyncData(() => ContentService.getBlogPostBySlug(slug), [slug])
}

export function useFeaturedBlogPosts() {
  return useAsyncData(() => ContentService.getFeaturedBlogPosts())
}

export function useRelatedBlogPosts(postId: string, limit?: number) {
  return useAsyncData(() => ContentService.getRelatedBlogPosts(postId, limit), [postId, limit])
}

// Testimonials hooks
export function useTestimonials() {
  return useAsyncData(() => ContentService.getTestimonials())
}

export function useFeaturedTestimonials() {
  return useAsyncData(() => ContentService.getFeaturedTestimonials())
}

export function useTestimonial(id: string) {
  return useAsyncData(() => ContentService.getTestimonialById(id), [id])
}

// Search hook
export function useContentSearch(query: string) {
  const [results, setResults] = useState<{
    services: Service[]
    portfolioItems: PortfolioItem[]
    blogPosts: BlogPost[]
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults(null)
      return
    }

    try {
      setLoading(true)
      setError(null)
      const searchResults = await ContentService.searchContent(searchQuery)
      setResults(searchResults)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      search(query)
    }, 300) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [query, search])

  return { results, loading, error, search }
}

// Categories hooks
export function useBlogCategories() {
  return useAsyncData(() => ContentService.getBlogCategories())
}

export function usePortfolioCategories() {
  return useAsyncData(() => ContentService.getPortfolioCategories())
}

// Pagination hook
export function usePagination<T>(
  fetchFunction: (page: number, limit: number) => Promise<PaginatedResponse<T>>,
  initialLimit: number = 10
) {
  const [currentPage, setCurrentPage] = useState(1)
  const [limit, setLimit] = useState(initialLimit)
  const [data, setData] = useState<T[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: initialLimit,
    total: 0,
    totalPages: 0,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async (page: number, pageLimit: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchFunction(page, pageLimit)
      setData(response.data)
      setPagination(response.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }, [fetchFunction])

  useEffect(() => {
    fetchData(currentPage, limit)
  }, [currentPage, limit, fetchData])

  const goToPage = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  const goToNextPage = useCallback(() => {
    if (currentPage < pagination.totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }, [currentPage, pagination.totalPages])

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }, [currentPage])

  const changeLimit = useCallback((newLimit: number) => {
    setLimit(newLimit)
    setCurrentPage(1) // Reset to first page when changing limit
  }, [])

  return {
    data,
    pagination,
    loading,
    error,
    currentPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    changeLimit,
    refetch: () => fetchData(currentPage, limit),
  }
}
