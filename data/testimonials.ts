import { Testimonial, Author } from '@/types'

const authors: Author[] = [
  {
    id: 'sarah-johnson',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'CEO & Founder at TechStart Inc.',
  },
  {
    id: 'micha<PERSON>-chen',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Creative Director at Creative Studios',
  },
  {
    id: 'emily-rod<PERSON><PERSON><PERSON>',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'VP of Marketing at Global Ventures',
  },
  {
    id: 'david-kim',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'CTO at InnovateTech',
  },
  {
    id: 'lisa-wang',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/placeholder.svg?height=60&width=60',
    bio: 'Lead Designer at Design Studio',
  },
]

export const testimonials: Testimonial[] = [
  {
    id: 'testimonial-1',
    quote: 'DSF Solutions transformed our digital presence completely. Their attention to detail and creative vision exceeded our expectations. The team delivered a website that not only looks stunning but also performs exceptionally well.',
    author: authors[0],
    company: 'TechStart Inc.',
    rating: 5,
    image: '/placeholder.svg?height=60&width=60',
    featured: true,
  },
  {
    id: 'testimonial-2',
    quote: 'Working with DSF was a game-changer for our business. They delivered a stunning website that perfectly captures our brand essence and has significantly improved our conversion rates.',
    author: authors[1],
    company: 'Creative Studios',
    rating: 5,
    image: '/placeholder.svg?height=60&width=60',
    featured: true,
  },
  {
    id: 'testimonial-3',
    quote: 'Professional, innovative, and results-driven. DSF Solutions is our go-to partner for all digital projects. Their expertise in both design and development is unmatched.',
    author: authors[2],
    company: 'Global Ventures',
    rating: 5,
    image: '/placeholder.svg?height=60&width=60',
    featured: true,
  },
  {
    id: 'testimonial-4',
    quote: 'The team at DSF Solutions understood our technical requirements perfectly and delivered a robust, scalable solution that has grown with our business.',
    author: authors[3],
    company: 'InnovateTech',
    rating: 5,
    image: '/placeholder.svg?height=60&width=60',
    featured: false,
  },
  {
    id: 'testimonial-5',
    quote: 'Their design process is thorough and collaborative. They took our vision and elevated it beyond what we imagined possible. Highly recommended!',
    author: authors[4],
    company: 'Design Studio',
    rating: 5,
    image: '/placeholder.svg?height=60&width=60',
    featured: false,
  },
]

export function getTestimonialById(id: string): Testimonial | undefined {
  return testimonials.find(testimonial => testimonial.id === id)
}

export function getFeaturedTestimonials(): Testimonial[] {
  return testimonials.filter(testimonial => testimonial.featured)
}

export function getTestimonialsByRating(minRating: number): Testimonial[] {
  return testimonials.filter(testimonial => testimonial.rating >= minRating)
}

export function getRandomTestimonials(count: number): Testimonial[] {
  const shuffled = [...testimonials].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}
