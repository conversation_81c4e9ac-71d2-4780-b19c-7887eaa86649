import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load homepage successfully', async ({ page }) => {
    await expect(page).toHaveTitle(/DSF Solutions/)
    await expect(page.locator('h1')).toContainText('CRAFTING')
  })

  test('should have proper navigation', async ({ page }) => {
    // Check if navigation items are present
    await expect(page.locator('nav')).toBeVisible()
    await expect(page.getByRole('button', { name: 'HOME' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'SERVICES' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'PORTFOLIO' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'CONTACT' })).toBeVisible()
  })

  test('should navigate to sections when clicking nav items', async ({ page }) => {
    // Click on Services navigation
    await page.getByRole('button', { name: 'SERVICES' }).click()
    
    // Wait for scroll and check if services section is visible
    await expect(page.locator('#services')).toBeInViewport()
    
    // Click on Portfolio navigation
    await page.getByRole('button', { name: 'PORTFOLIO' }).click()
    
    // Wait for scroll and check if portfolio section is visible
    await expect(page.locator('#portfolio')).toBeInViewport()
  })

  test('should display hero section with call-to-action buttons', async ({ page }) => {
    await expect(page.getByRole('button', { name: 'START YOUR PROJECT' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'VIEW OUR WORK' })).toBeVisible()
  })

  test('should display services section', async ({ page }) => {
    await expect(page.locator('#services')).toBeVisible()
    await expect(page.locator('#services h2')).toContainText('WHAT WE CREATE')
    
    // Check if service cards are present
    const serviceCards = page.locator('#services .group')
    await expect(serviceCards).toHaveCount(3) // Assuming 3 featured services
  })

  test('should display portfolio section', async ({ page }) => {
    await expect(page.locator('#portfolio')).toBeVisible()
    await expect(page.locator('#portfolio h2')).toContainText('OUR PORTFOLIO')
    
    // Check if portfolio items are present
    const portfolioCards = page.locator('#portfolio .group')
    await expect(portfolioCards.first()).toBeVisible()
  })

  test('should display testimonials section', async ({ page }) => {
    await expect(page.locator('#testimonials')).toBeVisible()
    await expect(page.locator('#testimonials h2')).toContainText('TRUSTED BY LEADERS')
    
    // Check if testimonial content is present
    await expect(page.locator('#testimonials blockquote')).toBeVisible()
  })

  test('should display contact section with form', async ({ page }) => {
    await expect(page.locator('#contact')).toBeVisible()
    await expect(page.locator('#contact h2')).toContainText('LET\'S COLLABORATE')
    
    // Check if contact form is present
    await expect(page.getByLabel('First Name')).toBeVisible()
    await expect(page.getByLabel('Last Name')).toBeVisible()
    await expect(page.getByLabel('Email Address')).toBeVisible()
    await expect(page.getByLabel('Project Details')).toBeVisible()
    await expect(page.getByRole('button', { name: 'SEND MESSAGE' })).toBeVisible()
  })

  test('should submit contact form successfully', async ({ page }) => {
    // Scroll to contact section
    await page.getByRole('button', { name: 'CONTACT' }).click()
    
    // Fill out the form
    await page.getByLabel('First Name').fill('John')
    await page.getByLabel('Last Name').fill('Doe')
    await page.getByLabel('Email Address').fill('<EMAIL>')
    await page.getByLabel('Project Details').fill('I need a new website for my business.')
    
    // Check privacy consent
    await page.getByLabel(/privacy policy/i).check()
    
    // Submit the form
    await page.getByRole('button', { name: 'SEND MESSAGE' }).click()
    
    // Wait for success message
    await expect(page.getByText(/thank you for your message/i)).toBeVisible({ timeout: 10000 })
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile navigation is present
    await expect(page.getByRole('button', { name: 'Toggle menu' })).toBeVisible()
    
    // Open mobile menu
    await page.getByRole('button', { name: 'Toggle menu' }).click()
    
    // Check if navigation items are visible in mobile menu
    await expect(page.getByRole('button', { name: 'HOME' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'SERVICES' })).toBeVisible()
  })

  test('should have proper accessibility', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1')
    await expect(h1).toHaveCount(1)
    
    // Check for alt text on images
    const images = page.locator('img')
    const imageCount = await images.count()
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i)
      await expect(img).toHaveAttribute('alt')
    }
    
    // Check for proper form labels
    await expect(page.getByLabel('First Name')).toBeVisible()
    await expect(page.getByLabel('Last Name')).toBeVisible()
    await expect(page.getByLabel('Email Address')).toBeVisible()
  })

  test('should load without console errors', async ({ page }) => {
    const consoleErrors: string[] = []
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })
    
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // Filter out known acceptable errors (like 404s for optional resources)
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('404') && 
      !error.includes('favicon') &&
      !error.includes('manifest')
    )
    
    expect(criticalErrors).toHaveLength(0)
  })

  test('should have good performance metrics', async ({ page }) => {
    // Navigate to page and wait for load
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Get performance metrics
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paintEntries = performance.getEntriesByType('paint')
      
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime,
      }
    })
    
    // Assert reasonable performance thresholds
    expect(metrics.loadTime).toBeLessThan(3000) // 3 seconds
    expect(metrics.domContentLoaded).toBeLessThan(2000) // 2 seconds
    if (metrics.firstContentfulPaint) {
      expect(metrics.firstContentfulPaint).toBeLessThan(2500) // 2.5 seconds
    }
  })
})
